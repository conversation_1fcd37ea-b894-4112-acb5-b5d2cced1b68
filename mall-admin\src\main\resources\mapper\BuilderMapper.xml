<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.admin.dao.BuilderMapper">

    <!-- 根据 builder_id 查询 -->
    <select id="selectByPrimaryKey" parameterType="int" resultType="com.macro.mall.common.model.Builder">
        SELECT
            builder_id AS builderId,
            builder_company AS builderCompany,
            builder_contact_name AS builderContactName,
            builder_contact_phone AS builderContactPhone,
            builder_contact_email AS builderContactEmail,
            create_time AS createTime,
            update_time AS updateTime
        FROM builder
        WHERE builder_id = #{builderId}
    </select>

    <!-- 根据 builder_id 更新（选择性更新） -->
    <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.common.model.Builder">
        UPDATE builder
        <set>
            <if test="builderCompany != null">builder_company = #{builderCompany},</if>
            <if test="builderContactName != null">builder_contact_name = #{builderContactName},</if>
            <if test="builderContactPhone != null">builder_contact_phone = #{builderContactPhone},</if>
            <if test="builderContactEmail != null">builder_contact_email = #{builderContactEmail},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE builder_id = #{builderId}
    </update>

    <!-- 根据 exhibitor_id 查询 builder 记录 -->
    <select id="selectByExhibitorId" parameterType="int" resultType="com.macro.mall.common.model.Builder">
        SELECT
            builder_id AS builderId,
            exhibitor_id AS exhibitorId,
            builder_company AS builderCompany,
            builder_contact_name AS builderContactName,
            builder_contact_phone AS builderContactPhone,
            builder_contact_email AS builderContactEmail,
            create_time AS createTime,
            update_time AS updateTime
        FROM builder
        WHERE exhibitor_id = #{exhibitorId}
            LIMIT 1
    </select>

    <!-- 新增 builder 记录，使用自增主键，插入时回填 builderId -->
    <insert id="insertSelective" parameterType="com.macro.mall.common.model.Builder" useGeneratedKeys="true" keyProperty="builderId">
        INSERT INTO builder
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="exhibitorId != null">exhibitor_id,</if>
            <if test="builderCompany != null">builder_company,</if>
            <if test="builderContactName != null">builder_contact_name,</if>
            <if test="builderContactPhone != null">builder_contact_phone,</if>
            <if test="builderContactEmail != null">builder_contact_email,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="exhibitorId != null">#{exhibitorId},</if>
            <if test="builderCompany != null">#{builderCompany},</if>
            <if test="builderContactName != null">#{builderContactName},</if>
            <if test="builderContactPhone != null">#{builderContactPhone},</if>
            <if test="builderContactEmail != null">#{builderContactEmail},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

</mapper>
