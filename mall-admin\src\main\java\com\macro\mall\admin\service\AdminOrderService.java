package com.macro.mall.admin.service;

import com.macro.mall.admin.dto.AdminOrderItemDTO;
import com.macro.mall.common.api.CommonPage;
import com.macro.mall.admin.dto.AdminOrderDTO;

import java.math.BigDecimal;

public interface AdminOrderService {

    /**
     * 根据展会ID分页查询订单列表（不包含订单项详情）
     * @param exhibitionId 展会ID
     * @param pageNum 当前页
     * @param pageSize 每页记录数
     * @return CommonPage 封装的分页结果
     */
    CommonPage<AdminOrderDTO> getOrdersByExhibitionId(Integer exhibitionId, int pageNum, int pageSize);

    /**
     * 根据订单ID查询订单详情（包含订单项详情）
     * @param orderId 订单ID
     * @return 订单详情 DTO
     */
    AdminOrderDTO getOrderById(Long orderId);

    /**
     * 获取某个展会下订单总数，采用 Redis 缓存
     * @param exhibitionId 展会ID
     * @return 总订单数
     */
    int getTotalOrderCount(Integer exhibitionId);



    int updateOrderItem(AdminOrderItemDTO orderItemDTO);
    int addOrderItem(AdminOrderItemDTO orderItemDTO);
    int updateSiteManagementFee(Long orderId, BigDecimal siteManagementFee);
    /**
     * 更新押金
     * @param orderId 订单ID
     * @param orderItemId 若为商品押金，传入对应的订单项ID；若为展览押金，此参数可为 null
     * @param deposit 新的押金金额
     * @param type "product" 或 "exhibition"
     */
    int updateDeposit(Long orderId, Long orderItemId, BigDecimal deposit, String type);


    /**
     * 删除订单项
     * @param orderItemId 订单项ID
     * @return 受影响的行数
     */
    int deleteOrderItem(Long orderItemId);




    int updateAuditStatus(Long orderId, String auditStatus);



    int updateRemark(Long orderId, String remark);

}
