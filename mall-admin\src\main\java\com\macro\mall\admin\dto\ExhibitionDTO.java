package com.macro.mall.admin.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class ExhibitionDTO {
    private Integer exhibitionId;
    private String name;
    private String nameEn;
    private String description;  // 原来的中文描述
    private String descriptionEn; // 新增的英文描述
    private String location;
    private String locationEn;
    private Date exhibitionStartTime;
    private Date exhibitionEndTime;
    private Date submissionStartTime;
    private Date submissionEndTime;
    private Integer allowUsdDisplay;
    private Integer showStandardExhibitorUploadMenu;
    private Integer allowInvoiceInfo;
    private Integer allowRefundInfo;
    private String coverImagePath;
    private BigDecimal usdToRmbRate;

    private List<ExhibitionRushRuleDTO> rushPeriods;

    public Integer getExhibitionId() {
        return exhibitionId;
    }

    public void setExhibitionId(Integer exhibitionId) {
        this.exhibitionId = exhibitionId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }



    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLocationEn() {
        return locationEn;
    }

    public void setLocationEn(String locationEn) {
        this.locationEn = locationEn;
    }

    public Date getExhibitionStartTime() {
        return exhibitionStartTime;
    }

    public void setExhibitionStartTime(Date exhibitionStartTime) {
        this.exhibitionStartTime = exhibitionStartTime;
    }

    public Date getExhibitionEndTime() {
        return exhibitionEndTime;
    }

    public void setExhibitionEndTime(Date exhibitionEndTime) {
        this.exhibitionEndTime = exhibitionEndTime;
    }

    public Date getSubmissionStartTime() {
        return submissionStartTime;
    }

    public void setSubmissionStartTime(Date submissionStartTime) {
        this.submissionStartTime = submissionStartTime;
    }

    public Date getSubmissionEndTime() {
        return submissionEndTime;
    }

    public void setSubmissionEndTime(Date submissionEndTime) {
        this.submissionEndTime = submissionEndTime;
    }

    public Integer getAllowUsdDisplay() {
        return allowUsdDisplay;
    }

    public void setAllowUsdDisplay(Integer allowUsdDisplay) {
        this.allowUsdDisplay = allowUsdDisplay;
    }

    public Integer getShowStandardExhibitorUploadMenu() {
        return showStandardExhibitorUploadMenu;
    }

    public void setShowStandardExhibitorUploadMenu(Integer showStandardExhibitorUploadMenu) {
        this.showStandardExhibitorUploadMenu = showStandardExhibitorUploadMenu;
    }

    public Integer getAllowInvoiceInfo() {
        return allowInvoiceInfo;
    }

    public void setAllowInvoiceInfo(Integer allowInvoiceInfo) {
        this.allowInvoiceInfo = allowInvoiceInfo;
    }

    public Integer getAllowRefundInfo() {
        return allowRefundInfo;
    }

    public void setAllowRefundInfo(Integer allowRefundInfo) {
        this.allowRefundInfo = allowRefundInfo;
    }

    public String getCoverImagePath() {
        return coverImagePath;
    }

    public void setCoverImagePath(String coverImagePath) {
        this.coverImagePath = coverImagePath;
    }

    public List<ExhibitionRushRuleDTO> getRushPeriods() {
        return rushPeriods;
    }

    public void setRushPeriods(List<ExhibitionRushRuleDTO> rushPeriods) {
        this.rushPeriods = rushPeriods;
    }

    public String getDescriptionEn() {
        return descriptionEn;
    }

    public void setDescriptionEn(String descriptionEn) {
        this.descriptionEn = descriptionEn;
    }
    @Override
    public String toString() {
        return "ExhibitionDTO{" +
                "exhibitionId=" + exhibitionId +
                ", name='" + name + '\'' +
                ", nameEn='" + nameEn + '\'' +
                ", description='" + description + '\'' +
                ", descriptionEn='" + descriptionEn + '\'' +
                ", location='" + location + '\'' +
                ", locationEn='" + locationEn + '\'' +
                ", exhibitionStartTime=" + exhibitionStartTime +
                ", exhibitionEndTime=" + exhibitionEndTime +
                ", submissionStartTime=" + submissionStartTime +
                ", submissionEndTime=" + submissionEndTime +
                ", allowUsdDisplay=" + allowUsdDisplay +
                ", showStandardExhibitorUploadMenu=" + showStandardExhibitorUploadMenu +
                ", allowInvoiceInfo=" + allowInvoiceInfo +
                ", allowRefundInfo=" + allowRefundInfo +
                ", coverImagePath='" + coverImagePath + '\'' +
                ", rushPeriods=" + rushPeriods +
                '}';
    }

    public BigDecimal getUsdToRmbRate() {
        return usdToRmbRate;
    }

    public void setUsdToRmbRate(BigDecimal usdToRmbRate) {
        this.usdToRmbRate = usdToRmbRate;
    }


    // getters and setters ...
}
