package com.macro.mall.admin.controller;

import com.macro.mall.admin.dto.AdminLoginParam;
import com.macro.mall.admin.service.AdminService;
import com.macro.mall.common.api.CommonResult;
import com.macro.mall.admin.dto.AdminLoginResponseDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 管理员登录 Controller（包括登录和注销接口）
 */
@RestController
@Api(tags = "AdminController", description = "管理员登录与注销")
@RequestMapping("/admin")
public class AdminController {

    @Autowired
    private AdminService adminService;

    @ApiOperation("管理员登录并返回 token 和展览 ID 列表")
    @PostMapping("/login")
    public CommonResult<AdminLoginResponseDTO> login(@RequestBody AdminLoginParam loginParam) {
        String username = loginParam.getUsername();
        String password = loginParam.getPassword();
        AdminLoginResponseDTO response = adminService.login(username, password);
        return CommonResult.success(response, "登录成功");
    }

    @ApiOperation("管理员注销")
    @PostMapping("/logout")
    public CommonResult<String> logout(@RequestBody Map<String, String> data) {
        String token = data.get("token");
        if (token == null || token.isEmpty()) {
            return CommonResult.validateFailed("Token 为空");
        }
        try {
            System.out.println("logout pressed, token: " + token);
            adminService.logout(token);
            return CommonResult.success(null, "注销成功");
        } catch (Exception e) {
            return CommonResult.failed("注销失败: " + e.getMessage());
        }
    }

}
