package com.macro.mall.admin.service.impl;

import com.macro.mall.common.model.EditText;
import com.macro.mall.admin.dao.EditMapper;
import com.macro.mall.admin.service.EditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class EditServiceImpl implements EditService {

    @Autowired
    private EditMapper editMapper;

    /**
     * 根据 exhibitionId 与 boothType 查询公告记录
     */
    @Override
    public EditText getById(Integer exhibitionId, String boothType) {
        // 参数校验
        if (exhibitionId == null || exhibitionId <= 0 || boothType == null || boothType.trim().isEmpty()) {
            System.err.println("Invalid exhibitionId or boothType: " + exhibitionId + ", boothType=" + boothType);
            return null;
        }
        return editMapper.getById(exhibitionId, boothType);
    }

    /**
     * 保存或更新公告记录
     * 如果记录（根据 exhibitionId 与 boothType）存在，则更新该记录；
     * 否则插入一条新记录。
     */
    @Transactional
    @Override
    public void saveOrUpdate(EditText text) {
        if (text == null || text.getExhibitionId() == null || text.getExhibitionId() <= 0 ||
                text.getBoothType() == null || text.getBoothType().trim().isEmpty()) {
            throw new IllegalArgumentException("无效的文本对象、展览ID或公告类型。");
        }

        // 根据 exhibitionId 与 boothType 查询记录是否存在
        EditText existingText = editMapper.getById(text.getExhibitionId(), text.getBoothType());
        if (existingText != null) {
            // 记录存在，执行更新操作
            System.out.println("Updating record for exhibitionId: " + text.getExhibitionId() +
                    ", boothType: " + text.getBoothType());
            editMapper.update(text);
        } else {
            // 记录不存在，插入新记录
            System.out.println("Inserting new record for exhibitionId: " + text.getExhibitionId() +
                    ", boothType: " + text.getBoothType());
            editMapper.insert(text);
        }
    }
}
