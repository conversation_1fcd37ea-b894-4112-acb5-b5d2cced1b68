package com.macro.mall.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("更新搭建商信息参数")
public class UpdateBuilderParam {

    @ApiModelProperty("搭建公司")
    private String builderCompany;
    @ApiModelProperty("联系人姓名")
    private String builderContactName;
    @ApiModelProperty("联系人电话")
    private String builderContactPhone;
    @ApiModelProperty("联系人邮箱")
    private String builderContactEmail;


    public String getBuilderCompany() {
        return builderCompany;
    }
    public void setBuilderCompany(String builderCompany) {
        this.builderCompany = builderCompany;
    }
    public String getBuilderContactName() {
        return builderContactName;
    }
    public void setBuilderContactName(String builderContactName) {
        this.builderContactName = builderContactName;
    }
    public String getBuilderContactPhone() {
        return builderContactPhone;
    }
    public void setBuilderContactPhone(String builderContactPhone) {
        this.builderContactPhone = builderContactPhone;
    }
    public String getBuilderContactEmail() {
        return builderContactEmail;
    }
    public void setBuilderContactEmail(String builderContactEmail) {
        this.builderContactEmail = builderContactEmail;
    }

}
