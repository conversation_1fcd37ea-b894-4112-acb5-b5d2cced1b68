package com.macro.mall.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel("更新展商信息(展台)参数")
public class UpdateExhibitorBoothParam {
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty("展商ID")
    private Long exhibitorId;
    @ApiModelProperty("公司名称")
    private String companyName;
    @ApiModelProperty("展商名称")
    private String exhibitorName;
    @ApiModelProperty("展位类型")
    private String boothType;
    @ApiModelProperty("展位号")
    private String boothNumber;
    @ApiModelProperty("展馆")
    private String exhibitionHall;
    @ApiModelProperty("展位面积")
    private BigDecimal boothArea;

    // Getters & Setters
    public Long getExhibitorId() {
        return exhibitorId;
    }
    public void setExhibitorId(Long exhibitorId) {
        this.exhibitorId = exhibitorId;
    }
    public String getCompanyName() {
        return companyName;
    }
    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    public String getExhibitorName() {
        return exhibitorName;
    }
    public void setExhibitorName(String exhibitorName) {
        this.exhibitorName = exhibitorName;
    }
    public String getBoothType() {
        return boothType;
    }
    public void setBoothType(String boothType) {
        this.boothType = boothType;
    }
    public String getBoothNumber() {
        return boothNumber;
    }
    public void setBoothNumber(String boothNumber) {
        this.boothNumber = boothNumber;
    }
    public String getExhibitionHall() {
        return exhibitionHall;
    }
    public void setExhibitionHall(String exhibitionHall) {
        this.exhibitionHall = exhibitionHall;
    }
    public BigDecimal getBoothArea() {
        return boothArea;
    }
    public void setBoothArea(BigDecimal boothArea) {
        this.boothArea = boothArea;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
}
