package com.macro.mall.admin.service;

import com.macro.mall.admin.dto.BlueprintDTO;
import com.macro.mall.admin.dto.BlueprintRequirementDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

public interface AdminBlueprintService {
    /**
     * 根据展会ID查询所有蓝图要求（仅要求数据，不包含具体上传记录）
     */
    List<BlueprintRequirementDTO> getRequirementsByExhibition(int exhibitionId) throws Exception;

    /**
     * 根据要求ID查询对应的所有上传记录
     */
    List<BlueprintDTO> getBlueprintsByRequirement(int requirementId) throws Exception;

    /**
     * 审核蓝图记录，仅更新状态（客户备注保持不变）
     */
    BlueprintDTO reviewBlueprint(int blueprintId, String status) throws Exception;

    /**
     * 新增：添加蓝图要求记录
     */
    BlueprintRequirementDTO addRequirement(int exhibitionId,
                                           String blueprintType,
                                           String description,
                                           String boothType,
                                           Date startTime,
                                           Date endTime,
                                           MultipartFile file) throws Exception;

    /**
     * 删除蓝图要求记录
     */
    void deleteRequirement(int requirementId) throws Exception;

    /**
     * 更新蓝图要求记录（仅更新普通字段，不包含文件操作）
     */
    BlueprintRequirementDTO updateRequirement(BlueprintRequirementDTO requirementDTO) throws Exception;

    /**
     * 更新蓝图要求中的文件（仅更新文件，不修改其它字段）的实现
     * @param exhibitionId 展会ID（用于生成新的 cosKey）
     * @param file 上传的新文件
     * @param requirementId 需求记录ID
     * @return 更新后的 BlueprintRequirementDTO 对象
     * @throws Exception 上传或更新过程中出现异常
     */
    BlueprintRequirementDTO uploadFileForUpdate(int exhibitionId, MultipartFile file, int requirementId,
                                                String blueprintType, String description, String boothType, Date startTime, Date endTime) throws Exception ;



}
