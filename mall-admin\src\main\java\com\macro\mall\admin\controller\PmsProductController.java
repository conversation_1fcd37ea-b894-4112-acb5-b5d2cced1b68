package com.macro.mall.admin.controller; //// com/macro/mall/controller/PmsProductController.java
//package com.macro.mall.portal.controller;
//
//import com.macro.mall.common.api.CommonResult;
//import com.macro.mall.model.PmsProduct;
//import com.macro.mall.portal.service.PmsProductService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//
///**
// * 商品管理Controller
// */
//@RestController
//@Api(tags = "PmsProductController")
//@Tag(name = "PmsProductController", description = "商品管理")
//@RequestMapping("/mall")
//public class PmsProductController {
//
//    private static final Logger logger = LoggerFactory.getLogger(PmsProductController.class);
//
//    @Autowired
//    private PmsProductService productService;
//
//    @ApiOperation("获取所有商品，支持筛选")
//    @GetMapping("/list")
//    public CommonResult<Map<String, Object>> listAll(
//            @RequestParam(value = "keyword", required = false) String keyword,
//            @RequestParam(value = "exhibitionid", required = false) Integer exhibitionid,
//            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
//            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
//        try {
//            System.out.println("===== Entering PmsProductController#listAll() =====");
//            List<PmsProduct> result = productService.listAllProducts(keyword, exhibitionid, pageNum, pageSize);
//            System.out.println("Result of listAllProducts: " + result);
//            // 构建符合前端需求的返回结构
//            Map<String, Object> response = new HashMap<>();
//            response.put("data", result);
//            int total = productService.countProducts(keyword, exhibitionid);
//            response.put("total", total);
//            return CommonResult.success(response);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return CommonResult.failed("服务器内部错误");
//        }
//    }
//
//    @ApiOperation("获取单个商品信息")
//    @GetMapping("/get/{id}")
//    public CommonResult<PmsProduct> getProduct(@PathVariable Long id) {
//        try {
//            PmsProduct product = productService.getProductById(id);
//            if (product != null) {
//                return CommonResult.success(product);
//            } else {
//                return CommonResult.failed("商品不存在");
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            return CommonResult.failed("服务器内部错误");
//        }
//    }
//
//
//}
