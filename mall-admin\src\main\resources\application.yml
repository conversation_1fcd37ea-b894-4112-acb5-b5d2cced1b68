    server:
      port: 8090
      servlet:
        encoding:
          force: true
          charset: UTF-8
          enabled: true

    spring:
      application:
        name: mall-admin
      profiles:
        active: dev # 默认使用开发环境
      mvc:
        pathmatch:
          matching-strategy: ant_path_matcher
      datasource:
        url:
        username: root
        password:

      redis:
        host:
        #    host: localhost # Redis服务器地址
        database: 0 # Redis数据库索引（默认为0）
        port: 6379 # Redis服务器连接端口
        password:
        timeout: 3000ms # 连接超时时间（毫秒）
      thymeleaf:
        mode: HTML5
        encoding: utf-8
        servlet:
          content-type: text/html
        cache: false
      servlet:
        multipart:
          max-file-size: 5MB
          max-request-size: 5MB

    mybatis:
      mapper-locations:
        - classpath:mapper/*.xml
        - classpath*:com/**/mapper/*.xml
      type-aliases-package: com.macro.mall.admin.model
      configuration:
        map-underscore-to-camel-case: true

    cos:
      secretId:
      secretKey:
      region:
      bucketName:

    jwt:
      tokenHeader:
      secret:
      expiration: 604800           # JWT 超期限时间(秒)，即 7 天
      tokenHead: 'Bearer'          # JWT 负载中拿到的前缀

    secure:
      ignored:
        urls:  # 安全路径白名单
          - /swagger-ui/
          - /swagger-resources/**
          - /**/v2/api-docs
          - /**/*.html
          - /**/*.js
          - /**/*.css
          - /**/*.png
          - /**/*.map
          - /favicon.ico
          - /druid/**
          - /actuator/**
          - /sso/**
          - /home/<USER>
          - /product/**
          - /brand/**
          - /alipay/**




    mongo:
      insert:
        sqlEnable: true  # 是否通过数据库数据插入到 MongoDB

    rabbitmq:
      queue:
        name:
          cancelOrder: cancelOrderQueue

    logging:
      level:
        root: info
        com.macro.mall: debug

    # DeepSeek AI 配置
    deepseek:
      api:
        key: ***********************************
        url: https://api.deepseek.com/chat/completions
        model: deepseek-chat
