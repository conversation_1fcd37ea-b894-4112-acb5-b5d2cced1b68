package com.macro.mall.admin.dao;

import com.macro.mall.common.model.Builder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface BuilderMapper {
    Builder selectByPrimaryKey(int builderId);
    int updateByPrimaryKeySelective(Builder record);
    // 通过 exhibitorId 查询 builder 记录
    Builder selectByExhibitorId(@Param("exhibitorId") int exhibitorId);

    // 新增记录，使用自增主键
    int insertSelective(Builder record);

    // 更新记录
}
