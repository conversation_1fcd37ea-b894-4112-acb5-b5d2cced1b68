package com.macro.mall.admin.dao;

import com.macro.mall.admin.model.Administrator;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AdminMapper {

    // 根据用户名查询管理员
    Administrator selectAdministratorByUsername(@Param("username") String username);

    // 根据管理员 ID 查询该管理员有权限的展览 ID 列表
    List<Integer> selectExhibitionIdsByAdminId(@Param("adminId") Integer adminId);
}
