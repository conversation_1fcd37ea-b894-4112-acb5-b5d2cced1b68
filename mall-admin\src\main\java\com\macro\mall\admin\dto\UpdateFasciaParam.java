package com.macro.mall.admin.dto;

import javax.validation.constraints.NotEmpty;

public class UpdateFasciaParam {

    @NotEmpty(message = "名称不能为空")
    private String name;

    @NotEmpty(message = "英文名称不能为空")
    private String nameEn;

    // 新增 logo 字段（存储上传后返回的 URL）
    private String logo;

    // Getter & Setter
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getNameEn() {
        return nameEn;
    }
    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }
    public String getLogo() {
        return logo;
    }
    public void setLogo(String logo) {
        this.logo = logo;
    }
}
