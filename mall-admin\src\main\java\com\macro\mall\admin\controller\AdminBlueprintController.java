package com.macro.mall.admin.controller;

import com.macro.mall.admin.dto.BlueprintDTO;
import com.macro.mall.admin.dto.BlueprintRequirementDTO;
import com.macro.mall.admin.service.AdminBlueprintService;
import com.macro.mall.common.api.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/admin/blueprint")
@Api(tags = "AdminBlueprintController", description = "蓝图需求及记录管理")
public class AdminBlueprintController {

    @Autowired
    private AdminBlueprintService adminBlueprintService;

    @ApiOperation("获取指定展会下的所有蓝图要求记录")
    @GetMapping("/requirements")
    @PreAuthorize("hasRole('ADMIN')")
    public CommonResult<List<BlueprintRequirementDTO>> getRequirements(
            @RequestParam("exhibitionId") int exhibitionId) {
        try {
            List<BlueprintRequirementDTO> list = adminBlueprintService.getRequirementsByExhibition(exhibitionId);
            return CommonResult.success(list);
        } catch (Exception e) {
            return CommonResult.failed(e.getMessage());
        }
    }

    @ApiOperation("获取指定要求下的所有蓝图上传记录")
    @GetMapping("/records")
    @PreAuthorize("hasRole('ADMIN')")
    public CommonResult<List<BlueprintDTO>> getBlueprintRecords(
            @RequestParam("requirementId") int requirementId) {
        try {
            List<BlueprintDTO> list = adminBlueprintService.getBlueprintsByRequirement(requirementId);
            return CommonResult.success(list);
        } catch (Exception e) {
            return CommonResult.failed(e.getMessage());
        }
    }

    @ApiOperation("审核蓝图记录（更新状态）")
    @PostMapping("/review")
    @PreAuthorize("hasRole('ADMIN')")
    public CommonResult<BlueprintDTO> reviewBlueprint(
            @RequestParam("blueprintId") int blueprintId,
            @RequestParam("status") String status) {
        try {
            BlueprintDTO dto = adminBlueprintService.reviewBlueprint(blueprintId, status);
            return CommonResult.success(dto);
        } catch (Exception e) {
            return CommonResult.failed(e.getMessage());
        }
    }

    @ApiOperation("新增蓝图要求记录")
    @PostMapping(value = "/requirement/add", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasRole('ADMIN')")
    public CommonResult<BlueprintRequirementDTO> addRequirement(
            @RequestParam("exhibitionId") int exhibitionId,
            @RequestParam("blueprintType") String blueprintType,
            @RequestParam("description") String description,
            @RequestParam("boothType") String boothType,
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestParam("startTime") Date startTime,
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestParam("endTime") Date endTime,
            @RequestParam("file") MultipartFile file) {
        try {
            BlueprintRequirementDTO result = adminBlueprintService.addRequirement(
                    exhibitionId, blueprintType, description, boothType, startTime, endTime, file);
            return CommonResult.success(result);
        } catch (Exception e) {
            return CommonResult.failed(e.getMessage());
        }
    }

    @ApiOperation("更新蓝图要求记录")
    @PostMapping("/requirement/update")
    @PreAuthorize("hasRole('ADMIN')")
    public CommonResult<BlueprintRequirementDTO> updateRequirement(
            @RequestBody BlueprintRequirementDTO requirementDTO) {
        try {
            BlueprintRequirementDTO result = adminBlueprintService.updateRequirement(requirementDTO);
            return CommonResult.success(result);
        } catch (Exception e) {
            return CommonResult.failed(e.getMessage());
        }
    }

    @ApiOperation("删除蓝图要求记录")
    @DeleteMapping("/requirement/delete")
    @PreAuthorize("hasRole('ADMIN')")
    public CommonResult<?> deleteRequirement(
            @RequestParam("requirementId") int requirementId) {
        try {
            adminBlueprintService.deleteRequirement(requirementId);
            return CommonResult.success("删除成功");
        } catch (Exception e) {
            return CommonResult.failed(e.getMessage());
        }
    }

    @ApiOperation("更新蓝图要求中的文件")
    @PostMapping("/requirement/uploadFileForUpdate")
    @PreAuthorize("hasRole('ADMIN')")
    public CommonResult<BlueprintRequirementDTO> uploadFileForUpdate(
            @RequestParam("exhibitionId") int exhibitionId,
            @RequestParam("requirementId") int requirementId,
            @RequestParam("blueprintType") String blueprintType,
            @RequestParam("description") String description,
            @RequestParam("boothType") String boothType,
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestParam("startTime") Date startTime,
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestParam("endTime") Date endTime,
            @RequestParam("file") MultipartFile file) {
        try {
            BlueprintRequirementDTO dto = adminBlueprintService.uploadFileForUpdate(
                    exhibitionId, file, requirementId, blueprintType, description, boothType, startTime, endTime);
            return CommonResult.success(dto);
        } catch (Exception e) {
            return CommonResult.failed(e.getMessage());
        }
    }

}
