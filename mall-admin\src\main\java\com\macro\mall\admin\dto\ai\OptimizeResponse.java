package com.macro.mall.admin.dto.ai;

/**
 * 后端返回给前端的优化结果
 */
public class OptimizeResponse {

    /** 优化后的标题 */
    private String optimizedTitle;

    /** 优化后的内容 HTML */
    private String optimizedContentHtml;

    /* ---------- 构造方法 ---------- */

    /** 无参构造 */
    public OptimizeResponse() {
    }

    /** 全参构造 */
    public OptimizeResponse(String optimizedContentHtml) {
        this.optimizedContentHtml = optimizedContentHtml;
    }

    /* ---------- Getter / Setter ---------- */

    public String getOptimizedTitle() {
        return optimizedTitle;
    }

    public void setOptimizedTitle(String optimizedTitle) {
        this.optimizedTitle = optimizedTitle;
    }

    public String getOptimizedContentHtml() {
        return optimizedContentHtml;
    }

    public void setOptimizedContentHtml(String optimizedContentHtml) {
        this.optimizedContentHtml = optimizedContentHtml;
    }
}
