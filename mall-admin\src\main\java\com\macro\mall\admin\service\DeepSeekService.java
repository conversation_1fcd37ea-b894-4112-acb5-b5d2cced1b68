package com.macro.mall.admin.service;


/**
 * DeepSeek AI 调用服务接口
 */
public interface DeepSeekService {

    /**
     * 调用 DeepSeek AI（指定模型）
     *
     * @param systemPrompt 系统提示词
     * @param userPrompt   用户提示词
     * @param model        模型名称
     * @return AI 返回内容
     */
    String callDeepSeekApi(String systemPrompt, String userPrompt, String model);

    /**
     * 调用 DeepSeek AI（使用默认模型）
     *
     * @param systemPrompt 系统提示词
     * @param userPrompt   用户提示词
     * @return AI 返回内容
     */
    String callDeepSeekApi(String systemPrompt, String userPrompt);
}
