package com.macro.mall.admin.controller;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.common.model.Product;
import com.macro.mall.admin.service.ProductService;
import com.macro.mall.common.service.CosService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/mall")
@Api(tags = "MallController", description = "商城商品管理接口（仅限管理员）")
@PreAuthorize("hasRole('ADMIN')")
public class MallController {

    @Autowired
    private ProductService productService;

    @Autowired
    private CosService cosService;

    @ApiOperation("获取商城商品列表（分页）")
    @GetMapping("/list")
    public CommonResult<Map<String, Object>> getProducts(
            @RequestParam("exhibitionId") Integer exhibitionId,
            @RequestParam("page") int page,
            @RequestParam("pageSize") int pageSize) {
        Map<String, Object> result = new HashMap<>();
        List<Product> products = productService.getProductsByExhibitionId(exhibitionId, page, pageSize);
        int totalCount = productService.getTotalProductCount(exhibitionId);
        result.put("products", products);
        result.put("total", totalCount);
        return CommonResult.success(result, "商品列表查询成功");
    }

    @ApiOperation("更新商品（支持图片文件上传）")
    @PutMapping(value = "/product/{productId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult<Product> updateProduct(
            @PathVariable("productId") Integer productId,
            @RequestPart("product") Product product,
            @RequestPart(value = "imageFile", required = false) MultipartFile imageFile) {

        boolean updated = productService.updateProduct(productId, product, imageFile);
        if (updated) {
            Product updatedProduct = productService.getProductById(productId);
            updatedProduct.setImage(cosService.getFullUrl(updatedProduct.getImage()));
            return CommonResult.success(updatedProduct, "商品更新成功");
        } else {
            return CommonResult.failed("商品更新失败");
        }
    }

    @ApiOperation("删除单个商品")
    @DeleteMapping("/product/{productId}")
    public CommonResult<String> deleteProduct(
            @PathVariable("productId") Integer productId,
            @RequestParam("exhibitionId") Integer exhibitionId) {

        boolean deleted = productService.deleteProduct(productId, exhibitionId);
        return deleted
                ? CommonResult.success("商品删除成功")
                : CommonResult.failed("商品删除失败");
    }

    @ApiOperation("批量删除商品")
    @DeleteMapping("/products")
    public CommonResult<String> deleteProducts(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Integer> productIds = (List<Integer>) request.get("productIds");
        Object eid = request.get("exhibitionId");
        Integer exhibitionId = (eid instanceof String)
                ? Integer.parseInt((String) eid)
                : (Integer) eid;

        boolean deleted = productService.deleteProducts(productIds, exhibitionId);
        return deleted
                ? CommonResult.success("批量删除商品成功")
                : CommonResult.failed("批量删除商品失败");
    }

    @ApiOperation("新增商品")
    @PostMapping("/product")
    public CommonResult<String> addProduct(@RequestBody Product product) {
        boolean added = productService.addProduct(product);
        return added
                ? CommonResult.success("商品新增成功")
                : CommonResult.failed("商品新增失败");
    }




    @ApiOperation("按展会汇率批量换算 USD")
    @PostMapping("/products/convertUsd")
    public CommonResult<String> convertUsd(
            @RequestBody Map<String, Object> req) {

        @SuppressWarnings("unchecked")
        List<Integer> productIds = (List<Integer>) req.get("productIds");
        Integer exhibitionId = (Integer) req.get("exhibitionId");

        boolean ok = productService.convertProductsToUsd(productIds, exhibitionId);
        return ok ? CommonResult.success("换算完成")
                : CommonResult.failed("换算失败，请检查汇率或商品数据");
    }

}
