package com.macro.mall.admin.dto;

import java.time.LocalDateTime;

/**
 * 用于和前端交互的 DTO
 */
public class ExhibitorDTO {
    private String companyName;
    private String phone;
    private String email;
    private String boothType;
    private String boothNumber;
    private String exhibitionHall;
    private Double boothArea;
    private String exhibitorName;
    private LocalDateTime loginTime;


    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getBoothType() {
        return boothType;
    }

    public void setBoothType(String boothType) {
        this.boothType = boothType;
    }

    public String getBoothNumber() {
        return boothNumber;
    }

    public void setBoothNumber(String boothNumber) {
        this.boothNumber = boothNumber;
    }

    public String getExhibitionHall() {
        return exhibitionHall;
    }

    public void setExhibitionHall(String exhibitionHall) {
        this.exhibitionHall = exhibitionHall;
    }

    public Double getBoothArea() {
        return boothArea;
    }

    public void setBoothArea(Double boothArea) {
        this.boothArea = boothArea;
    }

    public String getExhibitorName() {
        return exhibitorName;
    }

    public void setExhibitorName(String exhibitorName) {
        this.exhibitorName = exhibitorName;
    }

    public LocalDateTime getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(LocalDateTime loginTime) {
        this.loginTime = loginTime;
    }

    // Getter & Setter ...
    // （省略，可与 model 相同或仅保留需要的字段）
}
