package com.macro.mall.admin.dto.ai;

/**
 * 后端返回给前端的翻译结果
 */
public class TranslateResponse {

    /** 翻译后的英文标题 */
    private String englishTitle;

    /** 翻译后的英文内容 HTML */
    private String englishContentHtml;

    /* ---------- 构造方法 ---------- */

    /** 无参构造 */
    public TranslateResponse() {
    }

    /** 全参构造 */
    public TranslateResponse(String englishContentHtml) {
        this.englishContentHtml = englishContentHtml;
    }

    /* ---------- Getter / Setter ---------- */

    public String getEnglishTitle() {
        return englishTitle;
    }

    public void setEnglishTitle(String englishTitle) {
        this.englishTitle = englishTitle;
    }

    public String getEnglishContentHtml() {
        return englishContentHtml;
    }

    public void setEnglishContentHtml(String englishContentHtml) {
        this.englishContentHtml = englishContentHtml;
    }
}
