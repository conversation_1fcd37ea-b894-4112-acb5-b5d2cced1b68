package com.macro.mall.admin.service.impl;

import com.macro.mall.admin.dto.AdminOrderDTO;
import com.macro.mall.admin.dto.AdminOrderItemDTO;
import com.macro.mall.common.api.CommonPage;
import com.macro.mall.admin.dao.AdminOrderMapper;
import com.macro.mall.admin.service.AdminOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class AdminOrderServiceImpl implements AdminOrderService {

    @Autowired
    private AdminOrderMapper adminOrderMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public CommonPage<AdminOrderDTO> getOrdersByExhibitionId(Integer exhibitionId, int pageNum, int pageSize) {
        int offset = (pageNum - 1) * pageSize;
        Map<String, Object> params = new HashMap<>();
        params.put("exhibitionId", exhibitionId);
        params.put("offset", offset);
        params.put("pageSize", pageSize);

        List<AdminOrderDTO> list = adminOrderMapper.selectOrdersByExhibitionId(params);
        int total = getTotalOrderCount(exhibitionId);
        System.out.println("total"+total);
        CommonPage<AdminOrderDTO> result = new CommonPage<>();
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        result.setTotal((long) total);
        int totalPage = (int) Math.ceil(total * 1.0 / pageSize);
        result.setTotalPage(totalPage);
        result.setList(list);

        return result;
    }

    @Override
    public AdminOrderDTO getOrderById(Long orderId) {
        return adminOrderMapper.selectOrderById(orderId);
    }

    @Override
    public int getTotalOrderCount(Integer exhibitionId) {
        String key = "order_count:" + exhibitionId;
        Object countObj = redisTemplate.opsForValue().get(key);
        if (countObj != null) {
            return ((Long) countObj).intValue();
        }
        int count = adminOrderMapper.selectTotalOrderCount(exhibitionId);
        redisTemplate.opsForValue().set(key, (long) count, 60, TimeUnit.MINUTES);
        return count;
    }


    @Override
    public int updateOrderItem(AdminOrderItemDTO orderItemDTO) {
        return adminOrderMapper.updateOrderItem(orderItemDTO);
    }

    @Override
    public int addOrderItem(AdminOrderItemDTO orderItemDTO) {
        // 新增商品时，productId 固定为 -1
        orderItemDTO.setProductId(-1);
        return adminOrderMapper.addOrderItem(orderItemDTO);
    }

    @Override
    public int updateSiteManagementFee(Long orderId, BigDecimal siteManagementFee) {
        return adminOrderMapper.updateSiteManagementFee(orderId, siteManagementFee);
    }

    @Override
    public int updateDeposit(Long orderId, Long orderItemId, BigDecimal deposit, String type) {
        int count = 0;
        if ("product".equalsIgnoreCase(type)) {
            // 更新订单项中对应的押金，封装参数到 Map 中
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("orderItemId", orderItemId);
            paramMap.put("deposit", deposit);
            count = adminOrderMapper.updateProductDeposit(paramMap);
        } else if ("exhibition".equalsIgnoreCase(type)) {
            // 更新展览押金，封装参数到 Map 中
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("orderId", orderId);
            paramMap.put("deposit", deposit);
            count = adminOrderMapper.updateExhibitorDeposit(paramMap);
        }
        return count;
    }

    // 已有其他方法...

    @Override
    public int deleteOrderItem(Long orderItemId) {
        return adminOrderMapper.deleteOrderItem(orderItemId);
    }



    @Override
    public int updateAuditStatus(Long orderId, String auditStatus) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderId", orderId);
        paramMap.put("auditStatus", auditStatus);
        return adminOrderMapper.updateAuditStatus(paramMap);
    }

    @Override
    public int updateRemark(Long orderId, String remark) {
        Map<String, Object> paramMap2 = new HashMap<>();

        paramMap2.put("orderId", orderId);
        paramMap2.put("remark", remark);
        return adminOrderMapper.updateRemark(paramMap2);
    }


}
