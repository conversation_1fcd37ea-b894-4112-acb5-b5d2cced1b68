//package com.macro.mall.admin.controller;
//
//import com.macro.mall.common.api.CommonResult;
//import com.macro.mall.admin.dto.OrderCostDTO;
//import com.macro.mall.admin.dto.OrderDTO;
//import com.macro.mall.admin.service.ExhibitorService;
//import com.macro.mall.admin.service.OrderService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.core.context.SecurityContextHolder;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
//@RestController
//@RequestMapping("/api/orders")
//public class OrderController {
//
//    @Autowired
//    private OrderService orderService;
//
//    @Autowired
//    private ExhibitorService exhibitorService; // 用于通过username查询exhibitorId
//    /**
//     * 创建订单接口
//     * URL: POST /api/orders/create
//     */
//    @PostMapping("/create")
//    public CommonResult<OrderDTO> createOrder(@RequestBody OrderDTO orderDTO) {
//        OrderDTO createdOrder = orderService.createOrder(orderDTO);
//        if (createdOrder != null) {
//            return CommonResult.success(createdOrder, "订单创建成功");
//        } else {
//            return CommonResult.failed("订单创建失败");
//        }
//    }
//
//
//    /**
//     * 根据订单ID查询订单详情接口
//     * URL: GET /api/orders/{orderId}
//     */
//    @GetMapping("/{orderId}")
//    public CommonResult<OrderDTO> getOrder(@PathVariable Long orderId) {
//        OrderDTO order = orderService.getOrderById(orderId);
//        if (order != null) {
//            return CommonResult.success(order, "订单查询成功");
//        } else {
//            return CommonResult.failed("订单不存在");
//        }
//    }
//
//    /**
//     * 根据展商ID查询订单列表接口
//     * URL: GET /api/orders?exhibitorId=1
//     */
//    @GetMapping
//    public CommonResult<List<OrderDTO>> getOrdersByExhibitor() {
//        int exhibitorId = exhibitorService.getExhibitorIdByUsername(SecurityContextHolder.getContext().getAuthentication().getName());
//        if (exhibitorId == 0) {
//            return CommonResult.failed("展商错误");
//
//        }//在这里直接转换了
//
//        List<OrderDTO> orders = orderService.getOrdersByExhibitorId(exhibitorId);
//        return CommonResult.success(orders, "订单列表查询成功");
//    }
//
//    /**
//     * 计算订单价格（最终支付金额），并返回各项费用明细
//     * URL: POST /api/orders/calculate
//     */
//    @PostMapping("/calculate")
//    public CommonResult<OrderCostDTO> calculateOrder(@RequestBody OrderDTO orderDTO) {
//        OrderCostDTO costDto = orderService.calculateOrderCost(orderDTO);
//        return CommonResult.success(costDto, "订单费用计算成功");
//    }
//
//
//
//}
