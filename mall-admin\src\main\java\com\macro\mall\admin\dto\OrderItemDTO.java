package com.macro.mall.admin.dto;

import java.math.BigDecimal;

public class OrderItemDTO {
    private Integer productId;
    private Integer quantity;
    private BigDecimal unitPrice;
    // 如果商品有押金，也可以增加：
    private BigDecimal deposit;

    // getters and setters
    public Integer getProductId() {
        return productId;
    }
    public void setProductId(Integer productId) {
        this.productId = productId;
    }
    public Integer getQuantity() {
        return quantity;
    }
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }
    public BigDecimal getDeposit() {
        return deposit;
    }
    public void setDeposit(BigDecimal deposit) {
        this.deposit = deposit;
    }
}
