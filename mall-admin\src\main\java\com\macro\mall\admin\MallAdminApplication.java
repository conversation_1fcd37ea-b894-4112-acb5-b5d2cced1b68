package com.macro.mall.admin;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = {"com.macro.mall.common", "com.macro.mall.admin"})
@MapperScan("com.macro.mall.admin.dao") // 指定 Mapper 接口的包路径
public class MallAdminApplication{
	public static void main(String[] args) {
		SpringApplication.run(MallAdminApplication.class, args);
	}
}
