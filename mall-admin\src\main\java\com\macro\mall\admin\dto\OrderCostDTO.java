package com.macro.mall.admin.dto;

import java.math.BigDecimal;

public class OrderCostDTO{
    private BigDecimal productTotal;      // 商品总价（单价×数量，未包含押金）
    private BigDecimal siteManagementFee; // 场地管理费（按展位面积计算）
    private BigDecimal exhibitorDeposit;  // 展位押金（根据面积区间规则计算）
    private BigDecimal boothArea;
    // getters and setters
    public BigDecimal getProductTotal() {
        return productTotal;
    }
    public void setProductTotal(BigDecimal productTotal) {
        this.productTotal = productTotal;
    }
    public BigDecimal getSiteManagementFee() {
        return siteManagementFee;
    }
    public void setSiteManagementFee(BigDecimal siteManagementFee) {
        this.siteManagementFee = siteManagementFee;
    }
    public BigDecimal getExhibitorDeposit() {
        return exhibitorDeposit;
    }
    public void setExhibitorDeposit(BigDecimal exhibitorDeposit) {
        this.exhibitorDeposit = exhibitorDeposit;
    }

    public BigDecimal getBoothArea() {
        return boothArea;
    }

    public void setBoothArea(BigDecimal boothArea) {
        this.boothArea = boothArea;
    }
}
