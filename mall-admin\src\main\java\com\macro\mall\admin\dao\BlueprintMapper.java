package com.macro.mall.admin.dao;

import com.macro.mall.common.model.Blueprint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BlueprintMapper {
    int insert(Blueprint blueprint);

    // 根据要求ID查询所有上传记录
    List<Blueprint> selectByRequirementId(@Param("requirementId") int requirementId);

    // 根据蓝图ID查询单条记录
    Blueprint selectByPrimaryKey(@Param("blueprintId") int blueprintId);

    // 更新蓝图记录，仅更新状态字段（审核操作）
    int update(Blueprint blueprint);
}
