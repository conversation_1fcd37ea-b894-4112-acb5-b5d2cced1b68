package com.macro.mall.admin.controller;

import com.macro.mall.admin.dto.ExhibitionAnnouncementDTO;
import com.macro.mall.admin.dto.ExhibitionDTO;
import com.macro.mall.admin.dto.ExhibitionDepositRuleDTO;
import com.macro.mall.admin.service.ExhibitionService;
import com.macro.mall.common.api.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 展览相关管理接口
 */
@RestController
@RequestMapping("/admin/exhibition")
@Api(tags = "ExhibitionController", description = "展览管理接口（仅限管理员）")
@PreAuthorize("hasRole('ADMIN')")
public class ExhibitionController {

    @Autowired
    private ExhibitionService exhibitionService;

    /**
     * 根据 exhibitionId 查询展会信息
     */
    @ApiOperation("根据展会ID查询展会信息")
    @GetMapping
    public CommonResult<ExhibitionDTO> getExhibition(
            @RequestParam("exhibitionId") Integer exhibitionId) {
        System.out.println("Get Exhibition Info triggered for exhibitionId: " + exhibitionId);
        ExhibitionDTO exhibitionDTO = exhibitionService.getExhibitionDetailsById(exhibitionId);
        if (exhibitionDTO != null) {
            return CommonResult.success(exhibitionDTO);
        } else {
            return CommonResult.failed("未找到展览信息，exhibitionId：" + exhibitionId);
        }
    }

    /**
     * 更新展会信息（含加急规则）
     */
    @ApiOperation("更新展会信息（含加急规则）")
    @PostMapping
    public CommonResult<String> updateExhibition(@RequestBody ExhibitionDTO exhibitionDTO) {
        System.out.println("Received ExhibitionDTO: " + exhibitionDTO);
        System.out.println("Start Time: " + exhibitionDTO.getExhibitionStartTime());
        System.out.println("End Time: " + exhibitionDTO.getExhibitionEndTime());
        int result = exhibitionService.updateExhibition(exhibitionDTO);
        if (result > 0) {
            return CommonResult.success("更新成功");
        } else {
            return CommonResult.failed("更新失败");
        }
    }

    /**
     * 查询指定展会下的押金规则列表
     */
    @ApiOperation("查询指定展会下的押金规则列表")
    @GetMapping("/depositRule/list")
    public CommonResult<List<ExhibitionDepositRuleDTO>> listDepositRule(
            @RequestParam("exhibitionId") Integer exhibitionId) {
        List<ExhibitionDepositRuleDTO> rules = exhibitionService.listDepositRulesByExhibition(exhibitionId);
        return CommonResult.success(rules);
    }

    /**
     * 新增或修改押金规则
     */
    @ApiOperation("新增或修改押金规则")
    @PostMapping("/depositRule")
    public CommonResult<String> createOrUpdateDepositRule(
            @RequestBody ExhibitionDepositRuleDTO ruleDTO) {
        int count = exhibitionService.createOrUpdateDepositRule(ruleDTO);
        if (count > 0) {
            return CommonResult.success("操作成功");
        } else {
            return CommonResult.failed("操作失败");
        }
    }

    /**
     * 删除押金规则
     */
    @ApiOperation("删除押金规则")
    @DeleteMapping("/depositRule/{ruleId}")
    public CommonResult<String> deleteDepositRule(@PathVariable("ruleId") Integer ruleId) {
        int count = exhibitionService.deleteDepositRule(ruleId);
        if (count > 0) {
            return CommonResult.success("删除成功");
        } else {
            return CommonResult.failed("删除失败");
        }
    }


    @PostMapping("/cover")
    public CommonResult<Map<String, String>> uploadCover(
            @RequestParam Integer exhibitionId,
            @RequestPart MultipartFile file) {
        Map<String, String> map = exhibitionService.uploadCover(exhibitionId, file);
        return CommonResult.success(map);
    }


}
