package com.macro.mall.admin.dao;

import com.macro.mall.common.model.BlueprintRequirement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface BlueprintRequirementMapper {
    List<BlueprintRequirement> findByExhibitionId(@Param("exhibitionId") int exhibitionId);

    BlueprintRequirement selectByPrimaryKey(@Param("requirementId") int requirementId);

    int updateBlueprintRequirement(BlueprintRequirement requirement);

    // 新增：插入蓝图要求记录
    int insertBlueprintRequirement(BlueprintRequirement requirement);

    // 新增：删除蓝图要求记录
    int deleteBlueprintRequirement(@Param("requirementId") int requirementId);
}
