package com.macro.mall.admin.dao;

import com.macro.mall.common.model.Exhibitor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExhibitorMapper {

    /**
     * 根据 username 查询单个参展商
     */
    Exhibitor findByUsername(@Param("username") String username);

    /**
     * 根据主键查询
     */
    Exhibitor findById(@Param("exhibitorId") Long exhibitorId);

    /**
     * 插入一条新记录
     */
    int insertExhibitor(Exhibitor exhibitor);

    /**
     * 更新
     */
    int updateExhibitor(Exhibitor exhibitor);

    /**
     * (可选) 查询所有或带条件的
     */
    List<Exhibitor> findAll();

    /**
     * 根据展商ID查询展商信息
     * @param exhibitorId 展商ID
     * @return Exhibitor 对象
     */
    Exhibitor selectByExhibitorId(@Param("exhibitorId") int exhibitorId);
    // 你可根据业务需要继续添加其他方法……
}
