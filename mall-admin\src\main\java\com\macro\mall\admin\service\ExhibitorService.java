package com.macro.mall.admin.service;

import com.macro.mall.common.model.Exhibitor;

public interface ExhibitorService {
    /**
     * 通过 username 获取 exhibitorId
     * @param username 用户名
     * @return 查到则返回 exhibitorId，否则返回 null
     */
    int getExhibitorIdByUsername(String username);

    /**
     * (可选) 返回完整的 Exhibitor 对象
     */

    Exhibitor getExhibitorById(int exhibitorId);

    Exhibitor getByUsername(String username);

    // 其它需要的业务方法...
}
