package com.macro.mall.admin.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import java.math.BigDecimal;

public class ProductExcelDTO {
    @ExcelProperty("ChineseName")
    private String chineseName;
    @ExcelProperty("EnglishName")
    private String englishName;
    @ExcelProperty("Price")
    private BigDecimal price;
    @ExcelProperty("PriceUSD")
    private BigDecimal priceUsd;
    @ExcelProperty("Category")
    private String category;
    @ExcelProperty("EnglishCategory")
    private String englishCategory;
    @ExcelProperty("RemainingNumber")
    private Integer stock;
    @ExcelProperty("Description")
    private String description;
    @ExcelProperty("DescriptionEN")
    private String descriptionEn;
    @ExcelProperty("ImageUrl")
    private String imageUrl;
    @ExcelProperty("BoothType")
    private String boothType;

    // Getter and Setter for chineseName
    public String getChineseName() {
        return chineseName;
    }

    public void setChineseName(String chineseName) {
        this.chineseName = chineseName;
    }

    // Getter and Setter for englishName
    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    // Getter and Setter for price
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    // Getter and Setter for priceUsd
    public BigDecimal getPriceUsd() {
        return priceUsd;
    }

    public void setPriceUsd(BigDecimal priceUsd) {
        this.priceUsd = priceUsd;
    }

    // Getter and Setter for category
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    // Getter and Setter for englishCategory
    public String getEnglishCategory() {
        return englishCategory;
    }

    public void setEnglishCategory(String englishCategory) {
        this.englishCategory = englishCategory;
    }

    // Getter and Setter for stock
    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    // Getter and Setter for description
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    // Getter and Setter for descriptionEn
    public String getDescriptionEn() {
        return descriptionEn;
    }

    public void setDescriptionEn(String descriptionEn) {
        this.descriptionEn = descriptionEn;
    }

    // Getter and Setter for imageUrl
    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    // Getter and Setter for boothType
    public String getBoothType() {
        return boothType;
    }

    public void setBoothType(String boothType) {
        this.boothType = boothType;
    }
}
