package com.macro.mall.admin.dao;

import com.macro.mall.common.model.EditText;
import org.apache.ibatis.annotations.Mapper;

/**
 * MyBatis Mapper interface for database operations related to editor content.
 */
@Mapper
public interface EditMapper {

    /**
     * Selects a record from the 'editor' table based on the exhibition ID.
     * @param exhibitionId The ID of the exhibition to retrieve.
     * @return The corresponding Text object, or null if not found.
     */
    EditText getById(Integer exhibitionId,String boothType);

    /**
     * Inserts a new record into the 'editor' table.
     * @param text The Text object containing the data for the new record.
     */
    void insert(EditText text);

    /**
     * Updates an existing record in the 'editor' table based on the exhibition ID.
     * @param text The Text object containing the updated data.
     */
    void update(EditText text);

}
