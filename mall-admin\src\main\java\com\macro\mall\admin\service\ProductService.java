package com.macro.mall.admin.service;

import com.macro.mall.common.model.Product;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ProductService {
    /**
     * 根据展会ID获取商城中对应的商品列表
     * @param exhibitionId 展会ID
     * @return 商品列表
     */
    List<Product> getProductsByExhibitionId(Integer exhibitionId, int page, int pageSize);


    /**
     * 根据产品ID获取产品信息
     * @param productId 产品ID
     * @return 产品对象
     */
    Product getProductById(int productId);

    boolean updateProduct(Integer productId, Product product, MultipartFile imageFile);



    boolean deleteProduct(Integer productId,Integer exhibitionId);



    boolean deleteProducts(List<Integer> productIds, Integer exhibitionId);



    boolean addProduct(Product product);




    int getTotalProductCount(Integer exhibitionId);




    boolean convertProductsToUsd(List<Integer> ids, Integer exhibitionId);



}
