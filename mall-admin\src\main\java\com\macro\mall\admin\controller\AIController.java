package com.macro.mall.admin.controller;

import com.macro.mall.admin.dto.ai.*;
import com.macro.mall.admin.service.DeepSeekService;
import com.macro.mall.common.api.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 处理 AI 相关请求
 */
@RestController
@RequestMapping("/admin/ai")
@Api(tags = "AIController", description = "AI 服务接口（仅限管理员）")
@PreAuthorize("hasRole('ADMIN')")
public class AIController {

    private static final Logger log = LoggerFactory.getLogger(AIController.class);
    private final DeepSeekService deepSeekService;

    public AIController(DeepSeekService deepSeekService) {
        this.deepSeekService = deepSeekService;
    }

    /* ---------- 翻译 ---------- */
    @ApiOperation("将文本翻译为英文")
    @PostMapping("/translate")
    public CommonResult<TranslateResponse> translate(
            @Valid @RequestBody TranslateRequest req) {
        try {
            String contentEn = deepSeekService.callDeepSeekApi(null, req.getContentPrompt());
            if (contentEn != null) {
                return CommonResult.success(new TranslateResponse(contentEn), "翻译成功");
            }
            return CommonResult.failed("翻译失败：AI 服务返回结果不完整。");
        } catch (Exception e) {
            log.error("翻译异常", e);
            return CommonResult.failed("翻译异常: " + e.getMessage());
        }
    }

    /* ---------- 优化 ---------- */
    @ApiOperation("对文本内容进行优化处理")
    @PostMapping("/optimize")
    public CommonResult<OptimizeResponse> optimize(
            @Valid @RequestBody OptimizeRequest req) {
        try {
            String optHtml = deepSeekService.callDeepSeekApi(null, req.getContentPrompt());
            if (optHtml != null) {
                return CommonResult.success(new OptimizeResponse(optHtml), "优化成功");
            }
            return CommonResult.failed("优化失败：AI 服务返回结果不完整。");
        } catch (Exception e) {
            log.error("优化异常", e);
            return CommonResult.failed("优化异常: " + e.getMessage());
        }
    }

    /* ---------- 全局兜底 ---------- */
    @ExceptionHandler({ Exception.class })
    public CommonResult<Object> handleAll(Exception ex) {
        log.error("未捕获异常", ex);
        return CommonResult.failed("服务器内部错误: " + ex.getMessage());
    }

    /* ---------- 校验失败 ---------- */
    @ExceptionHandler(BindException.class)
    public CommonResult<Object> handleBind(BindException ex) {
        return CommonResult.validateFailed(
                ex.getBindingResult().getFieldError().getDefaultMessage());
    }
}
