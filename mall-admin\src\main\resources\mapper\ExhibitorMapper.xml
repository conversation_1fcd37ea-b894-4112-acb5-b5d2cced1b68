<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.admin.dao.ExhibitorMapper">
    <!-- 结果映射：这里不再映射 builderId 到 Exhibitor 对象 -->
    <resultMap id="exhibitorResultMap" type="com.macro.mall.common.model.Exhibitor">
        <id column="exhibitor_id" property="exhibitorId" />
        <result column="exhibition_id" property="exhibitionId" />
        <!-- 不映射 builderId，因为模型中没有 -->
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="company_name" property="companyName" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="login_time" property="loginTime" />
        <result column="booth_type" property="boothType" />
        <result column="booth_number" property="boothNumber" />
        <result column="exhibition_hall" property="exhibitionHall" />
        <result column="booth_area" property="boothArea" />
        <result column="exhibitor_name" property="exhibitorName" />
    </resultMap>

    <!-- 根据用户名查询 -->
    <select id="findByUsername" parameterType="string" resultMap="exhibitorResultMap">
        SELECT
            e.exhibitor_id,
            e.exhibition_id,
            e.username,
            e.password,
            e.company_name,
            e.phone,
            e.email,
            e.status,
            e.create_time,
            e.login_time,
            e.booth_type,
            e.booth_number,
            e.exhibition_hall,
            e.booth_area,
            e.exhibitor_name
        FROM exhibitor e
        WHERE e.username = #{username}
            LIMIT 1
    </select>

    <!-- 根据ID查询 -->
    <select id="findById" parameterType="int" resultMap="exhibitorResultMap">
        SELECT
            e.exhibitor_id,
            e.exhibition_id,
            e.username,
            e.password,
            e.company_name,
            e.phone,
            e.email,
            e.status,
            e.create_time,
            e.login_time,
            e.booth_type,
            e.booth_number,
            e.exhibition_hall,
            e.booth_area,
            e.exhibitor_name
        FROM exhibitor e
        WHERE e.exhibitor_id = #{exhibitorId}
    </select>


    <select id="selectByExhibitorId" resultMap="exhibitorResultMap" parameterType="int">
        SELECT *
        FROM exhibitor
        WHERE exhibitor_id = #{exhibitorId}
    </select>



    <!-- 根据exhibitorId查询booth_type -->
    <select id="selectBoothTypeByExhibitorId" resultType="String">
        SELECT booth_type
        FROM exhibitor
        WHERE exhibitor_id = #{exhibitorId}
    </select>


</mapper>
