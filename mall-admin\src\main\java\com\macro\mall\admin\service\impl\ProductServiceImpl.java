package com.macro.mall.admin.service.impl;

import com.macro.mall.admin.dao.ExhibitionMapper;
import com.macro.mall.admin.dao.ProductMapper;
import com.macro.mall.common.model.Product;
import com.macro.mall.admin.service.ProductService;
import com.macro.mall.common.service.CosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class ProductServiceImpl implements ProductService {

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private ExhibitionMapper exhibitionMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private CosService cosService;

    @Override
    public List<Product> getProductsByExhibitionId(Integer exhibitionId, int page, int pageSize) {
        int offset = (page - 1) * pageSize;  // 计算偏移量
        List<Product> products = productMapper.selectProductsByExhibitionId(exhibitionId, offset, pageSize);

        // 遍历每个商品，使用 cosService.getFullUrl 生成完整的图片 URL
        for (Product product : products) {
            // 假设 product.getImage() 返回的是 cosKey
            String fullUrl = cosService.getFullUrl(product.getImage());
            // 将完整的 URL 设置到 image 字段（前端依然接收 image 字段）
            product.setImage(fullUrl);
        }
        return products;
    }


    @Override
    public Product getProductById(int productId) {
        return productMapper.selectProductById(productId);
    }




    @Override
    public boolean updateProduct(Integer productId, Product product, MultipartFile imageFile) {
        System.out.println("Service: 开始更新商品，商品ID = " + productId);

        // 如果上传了新图片文件，则执行文件替换逻辑
        if (imageFile != null && !imageFile.isEmpty()) {
            System.out.println("Service: 检测到上传新图片，文件名 = " + imageFile.getOriginalFilename());
            // 查询原有的商品信息，获取原 COS 文件的 cosKey
            Product existingProduct = productMapper.selectProductById(productId);
            if (existingProduct != null) {
                System.out.println("Service: 查询到原商品信息 = " + existingProduct);
                if (existingProduct.getImage() != null && !existingProduct.getImage().isEmpty()) {
                    System.out.println("Service: 删除 COS 中原有的文件，cosKey = " + existingProduct.getImage());
                    cosService.deleteFile(existingProduct.getImage());
                } else {
                    System.out.println("Service: 原商品没有图片，无需删除");
                }
            } else {
                System.out.println("Service: 未查询到原商品信息，跳过删除操作");
            }
            // 生成新的 COS 文件 key，例如："exhibitionId/UploadProduct/timestamp_filename"
            String newKey = product.getExhibitionId() + "/UploadProduct/"
                    + System.currentTimeMillis() + "_" + imageFile.getOriginalFilename();
            System.out.println("Service: 生成新的 COS 文件 key = " + newKey);
            // 上传新文件到 COS；返回的 URL 可忽略，只需保存 cosKey 到数据库即可
            cosService.uploadFile(imageFile, newKey);
            System.out.println("Service: 上传新图片文件成功，更新商品 image 字段为新 cosKey");
            // 将新的 cosKey 设置到 product 的 image 字段中
            product.setImage(newKey);
        } else {
            // 若未上传新文件，则保持原有 image 值不变
            Product existingProduct = productMapper.selectProductById(productId);
            if (existingProduct != null) {
                System.out.println("Service: 未上传新图片文件，保持原有 image 字段 = " + existingProduct.getImage());
                product.setImage(existingProduct.getImage());
            } else {
                System.out.println("Service: 未查询到原商品信息，无法保持原有 image 字段");
            }
        }

        // 执行数据库更新操作
        int result = productMapper.updateProduct(productId, product);
        System.out.println("Service: 数据库更新返回结果 = " + result);
        return result > 0;
    }


    @Override
    public boolean deleteProduct(Integer productId, Integer exhibitionId) {
        // 查询商品信息，获取 cosKey
        Product product = productMapper.selectProductById(productId);
        if (product != null && product.getImage() != null && !product.getImage().isEmpty()) {
            System.out.println("Service: 删除商品前删除 COS 图片，cosKey = " + product.getImage());
            cosService.deleteFile(product.getImage());
        } else {
            System.out.println("Service: 删除商品时无图片需要删除");
        }

        int result = productMapper.deleteProduct(productId, exhibitionId);
        if (result > 0) {
            refreshProductCountCache(exhibitionId);
        }
        return result > 0;
    }

    @Override
    public boolean deleteProducts(List<Integer> productIds, Integer exhibitionId) {
        // 针对每个商品，先删除 COS 上对应的图片文件
        for (Integer productId : productIds) {
            Product product = productMapper.selectProductById(productId);
            if (product != null && product.getImage() != null && !product.getImage().isEmpty()) {
                System.out.println("Service: 批量删除时删除 COS 图片，商品ID = " + productId + ", cosKey = " + product.getImage());
                cosService.deleteFile(product.getImage());
            }
        }

        int result = productMapper.deleteProducts(productIds, exhibitionId);
        if (result > 0) {
            refreshProductCountCache(exhibitionId);
        }
        return result > 0;
    }

    @Override
    public boolean addProduct(Product product) {
        int result = productMapper.insertProduct(product);
        if (result > 0) {
            refreshProductCountCache(product.getExhibitionId());   // ← 调这里
        }
        return result > 0;
    }

    public int getTotalProductCount(Integer exhibitionId) {
        // 从 Redis 获取商品总数
        String key = "product_count:" + exhibitionId;
        Object countObj = redisTemplate.opsForValue().get(key); // 返回 Object 类型

        // 输出 Redis 中获取的商品总数及其类型
        System.out.println("Redis 中获取的商品总数值: " + countObj);
        System.out.println("Redis 中获取的商品总数类型: " + (countObj != null ? countObj.getClass().getName() : "null"));

        if (countObj != null) {
            // 如果 Redis 中存储的值是 Long 类型
            return ((Long) countObj).intValue();  // 转换为 int 返回
        }

        // 如果 Redis 中没有，查询数据库并更新缓存
        int count = productMapper.selectTotalProductCount(exhibitionId);
        redisTemplate.opsForValue().set(key, (long) count,60, TimeUnit.MINUTES); // 将商品总数存储为 Long 类型
        return count;
    }






    /** 重新统计某展会商品总数并写回 Redis（幂等，可失败不抛异常） */
    private void refreshProductCountCache(Integer exhibitionId) {
        try {
            int total = productMapper.selectTotalProductCount(exhibitionId);
            redisTemplate.opsForValue()
                    .set("product_count:" + exhibitionId,
                            (long) total, 60, TimeUnit.MINUTES);
        } catch (Exception ex) {
            System.out.println("刷新 Redis 商品总数失败: " + ex.getMessage());
            // 忽略异常：读侧会自动回源 DB
        }
    }




    @Override
    @Transactional
    public boolean convertProductsToUsd(List<Integer> ids, Integer exhibitionId) {
        BigDecimal rate = exhibitionMapper.selectRate(exhibitionId);
        if (rate == null || rate.compareTo(BigDecimal.ZERO) <= 0) return false;

        // 只更新 price_usd / deposit_usd 为空或 0 的商品
        List<Product> list = productMapper.selectProductsByIds(ids);
        for (Product p : list) {
            if (p.getPriceUsd() == null || p.getPriceUsd().signum() == 0) {
                p.setPriceUsd(p.getPrice().divide(rate, 2, RoundingMode.HALF_UP));
            }
            if (p.getDeposit() != null &&
                    (p.getDepositUsd() == null || p.getDepositUsd().signum() == 0)) {
                p.setDepositUsd(
                        p.getDeposit().divide(rate, 2, RoundingMode.HALF_UP));
            }
            productMapper.updateProductAmounts(p.getProductId(),
                    p.getPriceUsd(), p.getDepositUsd());
        }
        return true;
    }

}
