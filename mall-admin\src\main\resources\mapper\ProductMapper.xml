<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.admin.dao.ProductMapper">

    <!-- 定义 resultMap 映射 -->
    <resultMap id="productResultMap" type="com.macro.mall.common.model.Product">
        <result property="productId" column="product_id"/>
        <result property="exhibitionId" column="exhibition_id"/>
        <result property="productName" column="product_name"/>
        <result property="productNameEn" column="product_name_en"/>
        <result property="price" column="price"/>
        <result property="priceUsd" column="price_usd"/>
        <result property="deposit" column="deposit"/>
        <result property="depositUsd" column="deposit_usd"/>
        <result property="stock" column="stock"/>
        <result property="description" column="description"/>
        <result property="descriptionEn" column="description_en"/>
        <result property="category" column="category"/>
        <result property="categoryEn" column="category_en"/>
        <result property="image" column="image"/>
        <result property="boothType" column="booth_type"/>
    </resultMap>

    <!-- 根据展会ID查询对应商城下的商品列表 -->
    <select id="selectProductsByExhibitionId" resultMap="productResultMap">
        SELECT *
        FROM product
        WHERE exhibition_id = #{exhibitionId}
            LIMIT #{offset}, #{pageSize}
    </select>


    <!-- 根据产品ID查询产品信息 -->
    <select id="selectProductById" resultMap="productResultMap">
        SELECT
            product_id,
            exhibition_id,
            product_name,
            product_name_en,
            price,
            price_usd,
            deposit,
            deposit_usd,
            stock,
            description,
            description_en,
            category,
            category_en,
            image,
            booth_type
        FROM product
        WHERE product_id = #{productId}
    </select>

    <!-- 更新产品信息 -->
    <update id="updateProduct" parameterType="map">
        UPDATE
            product
        SET
            product_name = #{product.productName},
            product_name_en = #{product.productNameEn},
            price = #{product.price},
            price_usd = #{product.priceUsd},
            deposit = #{product.deposit},
            deposit_usd = #{product.depositUsd},
            stock = #{product.stock},
            description = #{product.description},
            description_en = #{product.descriptionEn},
            category = #{product.category},
            category_en = #{product.categoryEn},
        <if test="product.image != null">
            image = #{product.image},
        </if>
            booth_type = #{product.boothType}
        WHERE
            product_id = #{productId}
    </update>




    <delete id="deleteProduct" parameterType="map">
        DELETE FROM product
        WHERE product_id = #{productId}
          AND exhibition_id = #{exhibitionId}
    </delete>




    <delete id="deleteProducts" parameterType="map">
        DELETE FROM product
        WHERE product_id IN
        <foreach item="productId" collection="productIds" open="(" separator="," close=")">
            #{productId}
        </foreach>
        AND exhibition_id = #{exhibitionId}
    </delete>




    <insert id="insertProduct" parameterType="com.macro.mall.common.model.Product">
        INSERT INTO product (product_name, product_name_en, price, price_usd, deposit, deposit_usd, stock, description, description_en, category, category_en, image, booth_type, exhibition_id)
        VALUES (#{productName}, #{productNameEn}, #{price}, #{priceUsd}, #{deposit}, #{depositUsd}, #{stock}, #{description}, #{descriptionEn}, #{category}, #{categoryEn}, #{image}, #{boothType}, #{exhibitionId})
    </insert>
    <!-- 获取商品总数 -->
    <select id="selectTotalProductCount" resultType="int">
        SELECT COUNT(*) FROM product WHERE exhibition_id = #{exhibitionId}
    </select>




    <!-- ========== 新增：批量按 ID 查询 ========== -->
    <select id="selectProductsByIds" resultMap="productResultMap">
        SELECT *
        FROM product
        WHERE product_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- ========== 新增：只更新美元金额 ========== -->
    <update id="updateProductAmounts">
        UPDATE product
        SET price_usd   = #{priceUsd},
            deposit_usd = #{depositUsd}
        WHERE product_id = #{productId}
    </update>


</mapper>
