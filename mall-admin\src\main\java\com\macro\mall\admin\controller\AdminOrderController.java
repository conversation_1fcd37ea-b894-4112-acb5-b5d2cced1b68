package com.macro.mall.admin.controller;

import com.macro.mall.admin.dto.AdminOrderItemDTO;
import com.macro.mall.admin.dto.AdminOrderDTO;
import com.macro.mall.admin.service.AdminOrderService;
import com.macro.mall.common.api.CommonPage;
import com.macro.mall.common.api.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;

@RestController
@RequestMapping("/admin/orders")
@Api(tags = "AdminOrderController", description = "订单管理接口（仅限管理员）")
@PreAuthorize("hasRole('ADMIN')")
public class AdminOrderController {

    @Autowired
    private AdminOrderService adminOrderService;

    @ApiOperation("根据展会ID分页查询订单列表（不包含订单项详情）")
    @GetMapping
    public CommonResult<CommonPage<AdminOrderDTO>> getOrdersByExhibition(
            @RequestParam("exhibitionId") Integer exhibitionId,
            @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
            @RequestParam(value = "pageSize", defaultValue = "15") int pageSize) {
        CommonPage<AdminOrderDTO> orderPage = adminOrderService.getOrdersByExhibitionId(exhibitionId, pageNum, pageSize);
        return CommonResult.success(orderPage, "订单列表查询成功");
    }

    @ApiOperation("根据订单ID查询订单详情（包含订单项详情）")
    @GetMapping("/{orderId}")
    public CommonResult<AdminOrderDTO> getOrderById(@PathVariable("orderId") Long orderId) {
        AdminOrderDTO orderDTO = adminOrderService.getOrderById(orderId);
        if (orderDTO != null) {
            return CommonResult.success(orderDTO, "订单查询成功");
        } else {
            return CommonResult.failed("订单不存在");
        }
    }

    @ApiOperation("更新订单项（修改现有商品或押金等）")
    @PutMapping("/item")
    public CommonResult<?> updateOrderItem(@RequestBody AdminOrderItemDTO orderItemDTO) {
        int count = adminOrderService.updateOrderItem(orderItemDTO);
        if (count > 0) {
            return CommonResult.success(null, "订单项更新成功");
        } else {
            return CommonResult.failed("订单项更新失败");
        }
    }

    @ApiOperation("新增订单项（product_id=-1 时新增）")
    @PostMapping("/item")
    public CommonResult<?> addOrderItem(@RequestBody AdminOrderItemDTO orderItemDTO) {
        int count = adminOrderService.addOrderItem(orderItemDTO);
        if (count > 0) {
            return CommonResult.success(null, "订单项新增成功");
        } else {
            return CommonResult.failed("订单项新增失败");
        }
    }

    @ApiOperation("更新场地管理费")
    @PutMapping("/site-management-fee")
    public CommonResult<?> updateSiteManagementFee(@RequestBody Map<String, Object> paramMap) {
        Long orderId = ((Number) paramMap.get("orderId")).longValue();
        BigDecimal siteManagementFee = new BigDecimal(paramMap.get("siteManagementFee").toString());
        int count = adminOrderService.updateSiteManagementFee(orderId, siteManagementFee);
        if (count > 0) {
            return CommonResult.success(null, "场地管理费更新成功");
        } else {
            return CommonResult.failed("场地管理费更新失败");
        }
    }

    @ApiOperation("更新押金（支持 product 和 exhibition 两种类型）")
    @PutMapping("/deposit")
    public CommonResult<?> updateDeposit(@RequestBody Map<String, Object> paramMap) {
        Long orderId = ((Number) paramMap.get("orderId")).longValue();
        String type = paramMap.get("type").toString();
        BigDecimal deposit = new BigDecimal(paramMap.get("deposit").toString());
        Long orderItemId = paramMap.get("orderItemId") != null
                ? ((Number) paramMap.get("orderItemId")).longValue()
                : null;
        int count = adminOrderService.updateDeposit(orderId, orderItemId, deposit, type);
        if (count > 0) {
            return CommonResult.success(null, "押金更新成功");
        } else {
            return CommonResult.failed("押金更新失败");
        }
    }

    @ApiOperation("删除订单项（仅适用于无押金的订单项）")
    @DeleteMapping("/item/{orderItemId}")
    public CommonResult<?> deleteOrderItem(@PathVariable("orderItemId") Long orderItemId) {
        int count = adminOrderService.deleteOrderItem(orderItemId);
        if (count > 0) {
            return CommonResult.success(null, "订单项删除成功");
        } else {
            return CommonResult.failed("订单项删除失败");
        }
    }

    @ApiOperation("更新审核状态（审核通过/不通过）")
    @PutMapping("/audit-status")
    public CommonResult<?> updateAuditStatus(@RequestBody Map<String, Object> paramMap) {
        Long orderId = ((Number) paramMap.get("orderId")).longValue();
        String auditStatus = paramMap.get("auditStatus").toString();
        int count = adminOrderService.updateAuditStatus(orderId, auditStatus);
        if (count > 0) {
            return CommonResult.success(null, "审核状态更新成功");
        } else {
            return CommonResult.failed("审核状态更新失败");
        }
    }

    @ApiOperation("更新订单备注")
    @PutMapping("/remark")
    public CommonResult<?> updateRemark(@RequestBody Map<String, Object> paramMap) {
        Long orderId = ((Number) paramMap.get("orderId")).longValue();
        String remark = paramMap.get("remark").toString();
        int count = adminOrderService.updateRemark(orderId, remark);
        if (count > 0) {
            return CommonResult.success(null, "备注更新成功");
        } else {
            return CommonResult.failed("备注更新失败");
        }
    }
}
