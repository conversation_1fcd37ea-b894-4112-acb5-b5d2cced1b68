//package com.macro.mall.admin.service;
//
//import com.macro.mall.admin.dto.OrderCostDTO;
//import com.macro.mall.admin.dto.OrderDTO;
//import com.macro.mall.common.model.Order;
//import java.util.List;
//
//public interface OrderService {
//    /**
//     * 创建订单（包含订单项）
//     * @param orderDTO 订单传输对象
//     * @return 创建成功后的订单对象
//     */
//    OrderDTO createOrder(OrderDTO orderDTO);
//
//
//    OrderCostDTO calculateOrderCost(OrderDTO orderDTO);
//    /**
//     * 根据订单ID查询订单详情（含订单项）
//     * @param orderId 订单ID
//     * @return 订单详情
//     */
//    OrderDTO getOrderById(Long orderId);
//
//    /**
//     * 根据展商ID查询订单列表
//     * @param exhibitorId 展商ID
//     * @return 订单列表
//     */
//    List<OrderDTO> getOrdersByExhibitorId(int exhibitorId);
//
//
//
//
//
//
//
//
//
//
//}
