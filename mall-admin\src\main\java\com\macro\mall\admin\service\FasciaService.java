//package com.macro.mall.admin.service;
//
//import com.macro.mall.admin.dto.UpdateFasciaParam;
//import org.springframework.web.multipart.MultipartFile;
//
//public interface FasciaService {
//    /**
//     * 更新楣板信息
//     * 如果记录不存在则插入，如果存在则更新
//     * @param param 前端提交的参数，仅包含 name 和 nameEn
//     * @return 操作影响的记录数；返回 -1 表示 exhibitorId 获取失败
//     */
//    int updateFascia(UpdateFasciaParam param);
//
//
//    String uploadFasciaLogo(MultipartFile file);
//
//}
