package com.macro.mall.admin.service.impl;

import com.macro.mall.admin.dao.BuilderMapper;
import com.macro.mall.admin.dto.UpdateBuilderParam;
import com.macro.mall.common.model.Builder;
import com.macro.mall.admin.service.BuilderService;
import com.macro.mall.admin.service.ExhibitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import java.util.Date;

@Service
public class BuilderServiceImpl implements BuilderService {

    @Autowired
    private BuilderMapper builderMapper;

    @Autowired
    private ExhibitorService exhibitorService; // 用于通过username查询exhibitorId

    public int updateBuilder(UpdateBuilderParam param) {
        // 1. 查 exhibitorId
        int exhibitorId = exhibitorService.getExhibitorIdByUsername(
                SecurityContextHolder.getContext().getAuthentication().getName());
        if (exhibitorId == -1) {
            return -1; // 或者抛异常
        }
        System.out.println(exhibitorId);
        // 2. 根据 exhibitorId 去 builder 表查记录
        Builder builder = builderMapper.selectByExhibitorId(exhibitorId);

        Date now = new Date();
        if (builder == null) {
            // 2.1 如果没有记录，则执行插入
            builder = new Builder();
            builder.setExhibitorId(exhibitorId);
            builder.setBuilderCompany(param.getBuilderCompany());
            builder.setBuilderContactName(param.getBuilderContactName());
            builder.setBuilderContactPhone(param.getBuilderContactPhone());
            builder.setBuilderContactEmail(param.getBuilderContactEmail());
            builder.setCreateTime(now);
            builder.setUpdateTime(now);
            return builderMapper.insertSelective(builder);
        } else {
            // 2.2 如果已有记录，则执行更新
            builder.setBuilderCompany(param.getBuilderCompany());
            builder.setBuilderContactName(param.getBuilderContactName());
            builder.setBuilderContactPhone(param.getBuilderContactPhone());
            builder.setBuilderContactEmail(param.getBuilderContactEmail());
            builder.setUpdateTime(now);
            return builderMapper.updateByPrimaryKeySelective(builder);
        }
    }

}
