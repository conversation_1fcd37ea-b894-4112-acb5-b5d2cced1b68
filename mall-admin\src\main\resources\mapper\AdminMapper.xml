<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.admin.dao.AdminMapper">

    <!-- 定义结果映射 -->
    <resultMap id="AdministratorResultMap" type="com.macro.mall.admin.model.Administrator">
        <id property="adminId" column="admin_id" />
        <result property="companyId" column="company_id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="role" column="role"/>
        <result property="createTime" column="create_time"/>
        <result property="loginTime" column="login_time"/>
    </resultMap>

    <!-- 插入管理员记录，使用自动生成主键 -->
    <insert id="insertAdministrator" parameterType="com.macro.mall.admin.model.Administrator" useGeneratedKeys="true" keyProperty="adminId">
        INSERT INTO administrator
            (company_id, username, password, role, create_time, login_time)
        VALUES
            (#{companyId}, #{username}, #{password}, #{role}, #{createTime}, #{loginTime})
    </insert>

    <!-- 根据用户名查询管理员 -->
    <select id="selectAdministratorByUsername" parameterType="string" resultMap="AdministratorResultMap">
        SELECT admin_id, company_id, username, password, role, create_time, login_time
        FROM administrator
        WHERE username = #{username}
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.macro.mall.admin.model.Administrator">
        UPDATE administrator
        <set>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="role != null">role = #{role},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="loginTime != null">login_time = #{loginTime},</if>
        </set>
        WHERE admin_id = #{adminId}
    </update>


    <!-- 查询该管理员可以管理的展览 ID -->
    <select id="selectExhibitionIdsByAdminId" resultType="java.lang.Integer">
        SELECT exhibition_id
        FROM adminexhibition
        WHERE admin_id = #{adminId}
    </select>

</mapper>
