package com.macro.mall.admin.dao;

import com.macro.mall.common.model.ExhibitionDepositRule;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

public interface DepositRuleMapper {
    /**
     * 根据展会ID和展位面积查找对应的押金规则，
     * 条件：exhibition_id = #{exhibitionId} 且 areaMin <= #{area} < areaMax
     */
    ExhibitionDepositRule selectDepositRuleByExhibitionIdAndArea(@Param("exhibitionId") int exhibitionId,
                                                                 @Param("area") BigDecimal area);
}
