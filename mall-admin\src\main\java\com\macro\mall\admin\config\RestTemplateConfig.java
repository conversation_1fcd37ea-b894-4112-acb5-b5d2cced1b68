package com.macro.mall.admin.config;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * 全局 RestTemplate 配置
 */
@Configuration
public class RestTemplateConfig {

    /**
     * 定义 RestTemplate Bean，供 @Autowired / 构造注入使用
     */
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder
                // 统一设置连接/读取超时，可按需调整
                .setConnectTimeout(Duration.ofSeconds(10))
                .setReadTimeout(Duration.ofSeconds(60))
                .build();
    }
}
