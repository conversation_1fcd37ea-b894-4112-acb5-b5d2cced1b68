package com.macro.mall.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel("蓝图需求数据传输对象")
public class BlueprintRequirementDTO {
    @ApiModelProperty("需求ID")
    private int requirementId;

    @ApiModelProperty("展会ID")
    private int exhibitionId;

    @ApiModelProperty("蓝图类型")
    private String blueprintType;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("参考图路径")
    private String path;

    @ApiModelProperty("cosKey")
    private String cosKey;

    @ApiModelProperty("booth类型")
    private String boothType;


    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

    // Getters & Setters
    public int getRequirementId() {
        return requirementId;
    }

    public void setRequirementId(int requirementId) {
        this.requirementId = requirementId;
    }

    public int getExhibitionId() {
        return exhibitionId;
    }

    public void setExhibitionId(int exhibitionId) {
        this.exhibitionId = exhibitionId;
    }

    public String getBlueprintType() {
        return blueprintType;
    }

    public void setBlueprintType(String blueprintType) {
        this.blueprintType = blueprintType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }


    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getBoothType() {
        return boothType;
    }

    public void setBoothType(String boothType) {
        this.boothType = boothType;
    }

    public String getCosKey() {
        return cosKey;
    }

    public void setCosKey(String cosKey) {
        this.cosKey = cosKey;
    }
}
