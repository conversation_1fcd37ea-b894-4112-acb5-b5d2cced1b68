<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.admin.dao.ExhibitionMapper">

    <resultMap id="ExhibitionWithRushRulesResultMap" type="com.macro.mall.admin.dto.ExhibitionDTO">
        <!-- 展会基本信息 -->
        <id property="exhibitionId" column="exhibition_id"/>
        <result property="name" column="name"/>
        <result property="nameEn" column="name_en"/>
        <result property="description" column="description"/>
        <result property="descriptionEn" column="description_en"/>
        <result property="location" column="location"/>
        <result property="locationEn" column="location_en"/>
        <result property="exhibitionStartTime" column="exhibition_start_time"/>
        <result property="exhibitionEndTime" column="exhibition_end_time"/>
        <result property="submissionStartTime" column="submission_start_time"/>
        <result property="submissionEndTime" column="submission_end_time"/>
        <result property="allowUsdDisplay" column="allow_usd_display"/>
        <result property="showStandardExhibitorUploadMenu" column="show_standard_exhibitor_upload_menu"/>
        <result property="allowInvoiceInfo" column="allow_invoice_info"/>
        <result property="allowRefundInfo" column="allow_refund_info"/>
        <result property="coverImagePath" column="cover_image_path"/>
        <result property="usdToRmbRate" column="usd_to_rmb_rate"/>

        <!-- 加急规则集合 -->
        <collection property="rushPeriods" ofType="com.macro.mall.admin.dto.ExhibitionRushRuleDTO">
            <result property="rushRuleId" column="rush_rule_id"/>
            <result property="exhibitionId" column="exhibition_id"/>
            <result property="feePercentageRmb" column="fee_percentage_rmb"/>
            <result property="feePercentageUsd" column="fee_percentage_usd"/>
            <result property="rushStartTime" column="rush_start_time"/>
            <result property="rushEndTime" column="rush_end_time"/>
        </collection>

    </resultMap>

    <!-- 查询展会及其加急规则 -->
    <select id="selectExhibitionWithRushRules" resultMap="ExhibitionWithRushRulesResultMap">
        SELECT e.*,
               r.rush_rule_id, r.exhibition_id, r.fee_percentage_rmb, r.fee_percentage_usd,
               r.rush_start_time, r.rush_end_time
        FROM exhibition e
                 LEFT JOIN exhibition_rush_rule r ON e.exhibition_id = r.exhibition_id
        WHERE e.exhibition_id = #{exhibitionId}
    </select>

    <update id="updateExhibition">
        UPDATE exhibition
        SET
            name = #{name},
            name_en = #{nameEn},
            description = #{description},
            description_en = #{descriptionEn},
            location = #{location},
            location_en = #{locationEn},
            exhibition_start_time = #{exhibitionStartTime},
            exhibition_end_time = #{exhibitionEndTime},
            submission_start_time = #{submissionStartTime},
            submission_end_time = #{submissionEndTime},
            allow_usd_display = #{allowUsdDisplay},
            show_standard_exhibitor_upload_menu = #{showStandardExhibitorUploadMenu},
            allow_invoice_info = #{allowInvoiceInfo},
            allow_refund_info = #{allowRefundInfo},
            cover_image_path = #{coverImagePath},
            usd_to_rmb_rate   = #{usdToRmbRate}
        WHERE exhibition_id = #{exhibitionId}
    </update>

    <delete id="deleteRushRuleById">
        DELETE FROM exhibition_rush_rule WHERE rush_rule_id = #{rushRuleId}
    </delete>


    <insert id="insertRushRule">
        INSERT INTO exhibition_rush_rule (exhibition_id, rush_start_time, rush_end_time, fee_percentage_rmb, fee_percentage_usd)
        VALUES (#{exhibitionId}, #{rushStartTime}, #{rushEndTime}, #{feePercentageRmb}, #{feePercentageUsd})
    </insert>

    <update id="updateRushRule">
        UPDATE exhibition_rush_rule
        SET rush_start_time = #{rushStartTime},
            rush_end_time = #{rushEndTime},
            fee_percentage_rmb = #{feePercentageRmb},
            fee_percentage_usd = #{feePercentageUsd}
        WHERE rush_rule_id = #{rushRuleId}
    </update>


    <select id="selectRushRulesByExhibitionId" resultType="com.macro.mall.admin.dto.ExhibitionRushRuleDTO">
        SELECT * FROM exhibition_rush_rule WHERE exhibition_id = #{exhibitionId}
    </select>

        <!-- 下面是展位押金规则-->

    <!-- 查询某个展会的全部押金规则 -->
    <select id="selectDepositRulesByExhibitionId"
            resultType="com.macro.mall.common.model.ExhibitionDepositRule">
        SELECT
            rule_id,
            exhibition_id,
            area_min,
            area_max,
            deposit_price,
            deposit_price_usd,
            create_time,
            update_time
        FROM exhibition_deposit_rule
        WHERE exhibition_id = #{exhibitionId}
        ORDER BY area_min
    </select>

    <!-- 新增押金规则 -->
    <insert id="insertDepositRule" parameterType="com.macro.mall.common.model.ExhibitionDepositRule">
        INSERT INTO exhibition_deposit_rule (
            exhibition_id,
            area_min,
            area_max,
            deposit_price,
            deposit_price_usd,
            create_time,
            update_time
        )
        VALUES (
                   #{exhibitionId},
                   #{areaMin},
                   #{areaMax},
                   #{depositPrice},
                   #{depositPriceUsd},
                   NOW(),
                   NOW()
               )
    </insert>

    <!-- 修改押金规则 -->
    <update id="updateDepositRule" parameterType="com.macro.mall.common.model.ExhibitionDepositRule">
        UPDATE exhibition_deposit_rule
        SET
            exhibition_id = #{exhibitionId},
            area_min = #{areaMin},
            area_max = #{areaMax},
            deposit_price = #{depositPrice},
            deposit_price_usd = #{depositPriceUsd},
            update_time = NOW()
        WHERE rule_id = #{ruleId}
    </update>

    <!-- 删除押金规则 -->
    <delete id="deleteDepositRuleById">
        DELETE FROM exhibition_deposit_rule
        WHERE rule_id = #{ruleId}
    </delete>




    <select id="selectRate" resultType="java.math.BigDecimal">
        SELECT usd_to_rmb_rate
        FROM exhibition
        WHERE exhibition_id = #{exhibitionId}
    </select>


    <update id="updateCoverKey">
        UPDATE exhibition
        SET cover_image_path = #{cosKey}
        WHERE exhibition_id = #{exhibitionId}
    </update>


</mapper>
