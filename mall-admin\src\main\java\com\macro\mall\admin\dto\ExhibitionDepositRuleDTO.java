package com.macro.mall.admin.dto;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 展位押金规则DTO
 */
public class ExhibitionDepositRuleDTO {
    private Integer ruleId;
    private Integer exhibitionId;
    private BigDecimal areaMin;
    private BigDecimal areaMax;
    private BigDecimal depositPrice;
    private BigDecimal depositPriceUsd;
    private Date createTime;
    private Date updateTime;

    // Getter / Setter 略

    public Integer getRuleId() {
        return ruleId;
    }

    public void setRuleId(Integer ruleId) {
        this.ruleId = ruleId;
    }

    public Integer getExhibitionId() {
        return exhibitionId;
    }

    public void setExhibitionId(Integer exhibitionId) {
        this.exhibitionId = exhibitionId;
    }

    public BigDecimal getAreaMin() {
        return areaMin;
    }

    public void setAreaMin(BigDecimal areaMin) {
        this.areaMin = areaMin;
    }

    public BigDecimal getAreaMax() {
        return areaMax;
    }

    public void setAreaMax(BigDecimal areaMax) {
        this.areaMax = areaMax;
    }

    public BigDecimal getDepositPrice() {
        return depositPrice;
    }

    public void setDepositPrice(BigDecimal depositPrice) {
        this.depositPrice = depositPrice;
    }

    public BigDecimal getDepositPriceUsd() {
        return depositPriceUsd;
    }

    public void setDepositPriceUsd(BigDecimal depositPriceUsd) {
        this.depositPriceUsd = depositPriceUsd;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}
