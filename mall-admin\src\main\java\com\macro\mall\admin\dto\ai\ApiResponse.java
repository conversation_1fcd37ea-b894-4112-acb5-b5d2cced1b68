package com.macro.mall.admin.dto.ai;

/**
 * 通用的 API 响应包装类
 *
 * @param <T> 响应数据的具体类型
 */
public class ApiResponse<T> {

    /** 状态码 (例如 200 成功, 500 错误) */
    private int code;

    /** 提示信息 (例如 "操作成功", "请求失败") */
    private String msg;

    /** 实际的响应数据 */
    private T data;

    /* ---------- 构造方法 ---------- */

    public ApiResponse() {
    }

    public ApiResponse(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /* ---------- Getter / Setter ---------- */

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    /* ---------- 工具方法 ---------- */

    /** 成功的响应（默认提示） */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "操作成功", data);
    }

    /** 成功的响应（自定义提示） */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(200, message, data);
    }

    /** 失败的响应（自定义错误码） */
    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, message, null);
    }

    /** 失败的响应（默认错误码 500） */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(500, message, null);
    }
}
