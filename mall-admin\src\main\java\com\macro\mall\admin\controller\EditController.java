package com.macro.mall.admin.controller;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.common.model.EditText;
import com.macro.mall.admin.service.EditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/edit")
@Api(tags = "EditController", description = "公告内容管理接口（仅限管理员）")
@PreAuthorize("hasRole('ADMIN')")
public class EditController {

    @Autowired
    private EditService editService;

    /**
     * 根据 exhibitionId 与 boothType 获取公告内容
     * GET 请求示例：/admin/edit/123?boothType=both
     */
    @ApiOperation("根据展会ID和展位类型获取公告内容")
    @GetMapping("/{exhibitionId}")
    public CommonResult<EditText> getById(
            @PathVariable Integer exhibitionId,
            @RequestParam String boothType) {
        if (exhibitionId == null || exhibitionId <= 0 || boothType == null || boothType.trim().isEmpty()) {
            return CommonResult.failed("无效的展览ID或公告类型");
        }
        try {
            EditText text = editService.getById(exhibitionId, boothType);
            return CommonResult.success(text);
        } catch (Exception e) {
            System.err.println("Error during getById for exhibitionId " + exhibitionId + ": " + e.getMessage());
            e.printStackTrace();
            return CommonResult.failed("查询失败，发生意外错误。");
        }
    }

    /**
     * 保存或更新公告内容
     * 如果指定 exhibitionId 与 boothType 的记录存在，则更新记录；否则插入新记录。
     */
    @ApiOperation("保存或更新公告内容")
    @PostMapping
    public CommonResult<?> saveOrUpdate(@RequestBody EditText text) {
        try {
            editService.saveOrUpdate(text);
            return CommonResult.success("操作成功");
        } catch (IllegalArgumentException e) {
            System.err.println("Validation error during saveOrUpdate: " + e.getMessage());
            return CommonResult.failed("操作失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("Error during saveOrUpdate: " + e.getMessage());
            e.printStackTrace();
            return CommonResult.failed("操作失败，发生意外错误。");
        }
    }
}
