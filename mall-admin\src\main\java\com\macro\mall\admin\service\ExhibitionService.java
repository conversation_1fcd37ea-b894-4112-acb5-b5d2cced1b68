package com.macro.mall.admin.service;

import com.macro.mall.admin.dto.ExhibitionDTO;
import com.macro.mall.admin.dto.ExhibitionDepositRuleDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;


public interface ExhibitionService {
//    /**
//     * 根据展览ID获取公告信息
//     *
//     * @param exhibitionId 展览ID
//     * @return 展览公告DTO
//     */
//    ExhibitionAnnouncementDTO getExhibitionAnnouncement(Integer exhibitionId);

    int updateExhibition(ExhibitionDTO exhibitionDTO);
    ExhibitionDTO getExhibitionDetailsById(Integer exhibitionId);




    // =========== 新增的(押金规则) ==========

    List<ExhibitionDepositRuleDTO> listDepositRulesByExhibition(Integer exhibitionId);

    int createOrUpdateDepositRule(ExhibitionDepositRuleDTO ruleDTO);

    int deleteDepositRule(Integer ruleId);

    Map<String, String> uploadCover(Integer exhibitionId, MultipartFile file);

}
