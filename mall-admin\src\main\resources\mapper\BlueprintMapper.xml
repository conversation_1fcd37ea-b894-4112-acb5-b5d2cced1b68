<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.admin.dao.BlueprintMapper">
    <select id="selectByRequirementId" parameterType="int" resultType="com.macro.mall.common.model.Blueprint">
        SELECT * FROM blueprint WHERE requirement_id = #{requirementId}
    </select>

    <select id="selectByPrimaryKey" parameterType="int" resultType="com.macro.mall.common.model.Blueprint">
        SELECT * FROM blueprint WHERE blueprint_id = #{blueprintId}
    </select>

    <update id="update" parameterType="com.macro.mall.common.model.Blueprint">
        UPDATE blueprint
        SET status = #{status}
        WHERE blueprint_id = #{blueprintId}
    </update>

    <!-- 其它方法视业务需要添加 -->
</mapper>
