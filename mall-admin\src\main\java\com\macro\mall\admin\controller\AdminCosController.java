package com.macro.mall.admin.controller;

import com.macro.mall.common.api.CommonResult;
import com.macro.mall.common.dto.AdminFileDTO;
import com.macro.mall.common.service.CosService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/admin/cos")
@Api(tags = "AdminCosController", description = "COS 文件管理接口（仅限管理员）")
@PreAuthorize("hasRole('ADMIN')")  // 类级别保证所有接口都需管理员角色
public class AdminCosController {

    private final CosService cosService;

    @Autowired
    public AdminCosController(CosService cosService) {
        this.cosService = cosService;
    }

    @ApiOperation("获取指定展会下的所有文件列表")
    @GetMapping("/files")
    public CommonResult<List<AdminFileDTO>> listFiles(
            @RequestParam("exhibitionId") int exhibitionId) {
        List<AdminFileDTO> fileList = cosService.listFilesForAdmin(exhibitionId);
        return CommonResult.success(fileList);
    }

    @ApiOperation("根据文件 Key 获取预签名下载 URL")
    @GetMapping("/download")
    public CommonResult<Map<String, String>> downloadFile(
            @RequestParam("key") String key) {
        Map<String, String> result = cosService.downloadFile(key);
        return CommonResult.success(result);
    }
}
