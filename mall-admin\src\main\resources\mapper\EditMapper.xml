<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.admin.dao.EditMapper">

    <!-- 根据 exhibitionId 与 boothType 获取公告记录 -->
    <select id="getById" resultType="com.macro.mall.common.model.EditText">
        SELECT exhibition_id AS exhibitionId,
               booth_type AS boothType,
               title,
               title_en AS titleEn,
               content,
               content_en AS contentEn
        FROM editor
        WHERE exhibition_id = #{exhibitionId}
          AND booth_type = #{boothType}
    </select>

    <!-- 插入新的公告记录 -->
    <insert id="insert">
        INSERT INTO editor(exhibition_id, booth_type, title, title_en, content, content_en)
        VALUES (#{exhibitionId}, #{boothType}, #{title}, #{titleEn}, #{content}, #{contentEn})
    </insert>

    <!-- 更新已存在的公告记录（联合条件） -->
    <update id="update">
        UPDATE editor
        SET title = #{title},
            title_en = #{titleEn},
            content = #{content},
            content_en = #{contentEn}
        WHERE exhibition_id = #{exhibitionId}
          AND booth_type = #{boothType}
    </update>

</mapper>
