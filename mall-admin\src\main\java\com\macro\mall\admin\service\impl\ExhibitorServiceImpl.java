package com.macro.mall.admin.service.impl;

import com.macro.mall.admin.dao.ExhibitorMapper;
import com.macro.mall.common.model.Exhibitor;
import com.macro.mall.admin.service.ExhibitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ExhibitorServiceImpl implements ExhibitorService {

    @Autowired
    private ExhibitorMapper exhibitorMapper;

    @Override
    public int getExhibitorIdByUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            // 如果用户名为空，返回一个默认值，或者抛出异常
            return -1;
        }
        Exhibitor exhibitor = exhibitorMapper.findByUsername(username);
        // 如果没查到，则返回 -1，或者根据业务需求返回其他默认值
        return exhibitor != null ? exhibitor.getExhibitorId() : -1;
    }


    @Override
    public Exhibitor getByUsername(String username) {
        return exhibitorMapper.findByUsername(username);
    }
    @Override
    public Exhibitor getExhibitorById(int exhibitorId) {
        return exhibitorMapper.selectByExhibitorId(exhibitorId);
    }
    // 如果有插入、更新等操作可以加在这里
}
