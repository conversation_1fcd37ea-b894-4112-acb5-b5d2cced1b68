package com.macro.mall.admin.dao;

import com.macro.mall.admin.dto.ExhibitionDTO;
import com.macro.mall.admin.dto.ExhibitionRushRuleDTO;
import com.macro.mall.common.model.Exhibition;
import com.macro.mall.common.model.ExhibitionDepositRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExhibitionMapper {

    // 查询展会信息及加急规则的联合查询
    ExhibitionDTO selectExhibitionWithRushRules(@Param("exhibitionId") Integer exhibitionId);
    // 查询展会下的所有加急规则
    List<ExhibitionRushRuleDTO> selectRushRulesByExhibitionId(@Param("exhibitionId") Integer exhibitionId);

    // 更新展会信息
    int updateExhibition(Exhibition exhibition);

    // 更新加急规则
    int updateRushRule(@Param("rushRuleId") Integer rushRuleId,
                       @Param("rushStartTime") java.util.Date rushStartTime,
                       @Param("rushEndTime") java.util.Date rushEndTime,
                       @Param("feePercentageRmb") Double feePercentageRmb,
                       @Param("feePercentageUsd") Double feePercentageUsd);

    // 删除指定的加急规则
    int deleteRushRuleById(@Param("rushRuleId") Integer rushRuleId);

    // 插入加急规则
    int insertRushRule(@Param("exhibitionId") Integer exhibitionId,
                       @Param("rushStartTime") java.util.Date rushStartTime,
                       @Param("rushEndTime") java.util.Date rushEndTime,
                       @Param("feePercentageRmb") Double feePercentageRmb,
                       @Param("feePercentageUsd") Double feePercentageUsd);







    // ============ 下面是新加的(押金规则) ============

    List<ExhibitionDepositRule> selectDepositRulesByExhibitionId(@Param("exhibitionId") Integer exhibitionId);

    int insertDepositRule(ExhibitionDepositRule depositRule);

    int updateDepositRule(ExhibitionDepositRule depositRule);

    int deleteDepositRuleById(@Param("ruleId") Integer ruleId);

    //
    /** 获取展会级汇率 1 USD = ? RMB */
    java.math.BigDecimal selectRate(@Param("exhibitionId") Integer exhibitionId);


    int updateCoverKey(@Param("exhibitionId") Integer exhibitionId,
                       @Param("cosKey")      String  cosKey);


}
