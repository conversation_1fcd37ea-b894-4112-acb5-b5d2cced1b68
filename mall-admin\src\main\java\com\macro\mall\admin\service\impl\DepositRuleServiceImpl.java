package com.macro.mall.admin.service.impl;

import com.macro.mall.admin.dao.DepositRuleMapper;
import com.macro.mall.common.model.ExhibitionDepositRule;
import com.macro.mall.admin.service.DepositRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class DepositRuleServiceImpl implements DepositRuleService {

    @Autowired
    private DepositRuleMapper depositRuleMapper;

    @Override
    public BigDecimal getDepositRate(int exhibitionId, BigDecimal area) {
        ExhibitionDepositRule rule = depositRuleMapper.selectDepositRuleByExhibitionIdAndArea(exhibitionId, area);
        if (rule != null) {
            return rule.getDepositPrice();
        }
        // 若未找到规则，可返回默认值，或抛出异常；此处返回 0
        return BigDecimal.ZERO;
    }
}
