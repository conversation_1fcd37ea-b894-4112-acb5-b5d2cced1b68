package com.macro.mall.admin.service.impl;

import com.macro.mall.admin.dao.BlueprintMapper;
import com.macro.mall.admin.dao.BlueprintRequirementMapper;
import com.macro.mall.admin.dao.ExhibitorMapper;
import com.macro.mall.admin.dao.BuilderMapper;
import com.macro.mall.admin.dto.BlueprintDTO;
import com.macro.mall.admin.dto.BlueprintRequirementDTO;
import com.macro.mall.admin.service.AdminBlueprintService;
import com.macro.mall.common.model.Blueprint;
import com.macro.mall.common.model.BlueprintRequirement;
import com.macro.mall.common.model.Exhibitor;
import com.macro.mall.common.model.Builder;
import com.macro.mall.common.service.CosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class AdminBlueprintServiceImpl implements AdminBlueprintService {

    @Autowired
    private BlueprintRequirementMapper blueprintRequirementMapper;
    @Autowired
    private BlueprintMapper blueprintMapper;
    @Autowired
    private CosService cosService;
    @Autowired
    private ExhibitorMapper exhibitorMapper;
    @Autowired
    private BuilderMapper builderMapper;

    /**
     * 根据展会ID查询所有蓝图要求记录（仅返回要求数据，不包含具体上传记录）
     */
    @Override
    public List<BlueprintRequirementDTO> getRequirementsByExhibition(int exhibitionId) throws Exception {
        System.out.println("开始查询展会ID为 " + exhibitionId + " 的蓝图要求...");
        List<BlueprintRequirement> reqList = blueprintRequirementMapper.findByExhibitionId(exhibitionId);
        System.out.println("查询到 " + reqList.size() + " 条要求记录。");
        List<BlueprintRequirementDTO> dtoList = new ArrayList<>();
        for (BlueprintRequirement req : reqList) {
            System.out.println("处理要求ID: " + req.getRequirementId());
            BlueprintRequirementDTO dto = convertToDTO(req);
            dtoList.add(dto);
        }
        System.out.println("要求数据封装完成，共封装 " + dtoList.size() + " 条记录。");
        return dtoList;
    }

    /**
     * 根据要求ID查询所有对应的上传记录（要求详情页面）
     */
    @Override
    public List<BlueprintDTO> getBlueprintsByRequirement(int requirementId) throws Exception {
        System.out.println("开始查询要求ID为 " + requirementId + " 的所有上传记录...");
        List<Blueprint> bpList = blueprintMapper.selectByRequirementId(requirementId);
        System.out.println("查询到 " + (bpList == null ? 0 : bpList.size()) + " 条上传记录。");
        List<BlueprintDTO> dtoList = new ArrayList<>();
        if (bpList != null) {
            for (Blueprint bp : bpList) {
                dtoList.add(convertToDTO(bp));
            }
        }
        return dtoList;
    }

    /**
     * 审核蓝图记录，仅更新状态（客户备注保持不变）
     */
    @Override
    public BlueprintDTO reviewBlueprint(int blueprintId, String status) throws Exception {
        System.out.println("开始审核蓝图记录，blueprintId: " + blueprintId + ", 新状态: " + status);
        Blueprint blueprint = blueprintMapper.selectByPrimaryKey(blueprintId);
        if (blueprint == null) {
            System.out.println("未找到蓝图记录，blueprintId: " + blueprintId);
            throw new Exception("蓝图记录不存在");
        }
        System.out.println("原状态: " + blueprint.getStatus() + ", 客户备注: " + blueprint.getReviewComments());
        blueprint.setStatus(status);
        int count = blueprintMapper.update(blueprint);
        if (count <= 0) {
            System.out.println("蓝图记录更新失败，blueprintId: " + blueprintId);
            throw new Exception("审核状态更新失败");
        }
        System.out.println("审核更新成功，blueprintId: " + blueprintId + ", 更新后状态: " + blueprint.getStatus());
        return convertToDTO(blueprint);
    }


    /**
     * 修改蓝图要求记录
     */
    @Override
    public BlueprintRequirementDTO updateRequirement(BlueprintRequirementDTO requirementDTO) throws Exception {
        System.out.println("开始更新要求ID: " + requirementDTO.getRequirementId());

        // 校验时间：结束时间不能早于开始时间
        if (requirementDTO.getEndTime().before(requirementDTO.getStartTime())) {
            throw new Exception("结束时间不能早于开始时间");
        }

        BlueprintRequirement req = new BlueprintRequirement();
        req.setRequirementId(requirementDTO.getRequirementId());
        req.setExhibitionId(requirementDTO.getExhibitionId());
        req.setBlueprintType(requirementDTO.getBlueprintType());
        req.setDescription(requirementDTO.getDescription());
        req.setStartTime(requirementDTO.getStartTime());
        req.setEndTime(requirementDTO.getEndTime());
        req.setBoothType(requirementDTO.getBoothType());
        System.out.println("cosKey: " + requirementDTO.getCosKey());

        int count = blueprintRequirementMapper.updateBlueprintRequirement(req);
        if (count <= 0) {
            System.out.println("更新要求记录失败，requirementId: " + requirementDTO.getRequirementId());
            throw new Exception("更新蓝图要求失败");
        }
        System.out.println("要求记录更新成功，requirementId: " + requirementDTO.getRequirementId());
        BlueprintRequirement updated = blueprintRequirementMapper.selectByPrimaryKey(req.getRequirementId());
        return convertToDTO(updated);
    }

    /**
     * 删除蓝图要求记录
     */
    @Override
    public void deleteRequirement(int requirementId) throws Exception {
        System.out.println("开始删除蓝图要求，requirementId: " + requirementId);
        int count = blueprintRequirementMapper.deleteBlueprintRequirement(requirementId);
        if (count <= 0) {
            System.out.println("删除蓝图要求失败，requirementId: " + requirementId);
            throw new Exception("删除蓝图要求失败");
        }
        System.out.println("删除成功，requirementId: " + requirementId);
    }

    /**
     * 将 BlueprintRequirement 模型转换为 DTO，调用 cosService 拼凑完整文件路径
     */
    private BlueprintRequirementDTO convertToDTO(BlueprintRequirement req) {
        BlueprintRequirementDTO dto = new BlueprintRequirementDTO();
        dto.setRequirementId(req.getRequirementId());
        dto.setExhibitionId(req.getExhibitionId());
        dto.setBlueprintType(req.getBlueprintType());
        dto.setDescription(req.getDescription());
        // 通过 cosService.getFullUrl() 拼凑完整 URL 返回给前端
        dto.setPath(cosService.getFullUrl(req.getCosKey()));
        dto.setStartTime(req.getStartTime());
        dto.setEndTime(req.getEndTime());
        dto.setBoothType(req.getBoothType());
        return dto;
    }

    /**
     * 将 Blueprint 模型转换为 BlueprintDTO，同时联查 exhibitor 与 builder 数据
     */
    private BlueprintDTO convertToDTO(Blueprint blueprint) {
        BlueprintDTO dto = new BlueprintDTO();
        dto.setBlueprintId(blueprint.getBlueprintId());
        dto.setRequirementId(blueprint.getRequirementId());
        dto.setBlueprintType(blueprint.getBlueprintType());
        dto.setStatus(blueprint.getStatus());
        // 通过 cosService 拼凑完整文件路径返回给前端
        dto.setFilePath(cosService.getFullUrl(blueprint.getCosKey()));

        // 联查 exhibitor 数据
        Exhibitor exhibitor = exhibitorMapper.selectByExhibitorId(blueprint.getExhibitorId());
        if (exhibitor != null) {
            dto.setExhibitorCompany(exhibitor.getCompanyName());
            dto.setExhibitorPhone(exhibitor.getPhone());
            dto.setExhibitorName(exhibitor.getExhibitorName());
        } else {
            System.out.println("未查询到 exhibitor 数据, exhibitorId: " + blueprint.getExhibitorId());
        }

        // 联查 builder 数据
        Builder builder = builderMapper.selectByExhibitorId(blueprint.getExhibitorId());
        if (builder != null) {
            dto.setBuilderCompany(builder.getBuilderCompany());
            dto.setBuilderContactName(builder.getBuilderContactName());
            dto.setBuilderContactPhone(builder.getBuilderContactPhone());
        } else {
            System.out.println("未查询到 builder 数据, exhibitorId: " + blueprint.getExhibitorId());
        }
        return dto;
    }



    /**
     * 更新蓝图要求中的文件
     * 接收参数：exhibitionId、上传的文件 file，以及需求记录的 requirementId
     */
    @Override
    public BlueprintRequirementDTO uploadFileForUpdate(int exhibitionId, MultipartFile file, int requirementId,
                                                       String blueprintType, String description, String boothType,
                                                       Date startTime, Date endTime) throws Exception {
        // 校验结束时间不能早于开始时间
        if (endTime.before(startTime)) {
            throw new Exception("结束时间不能早于开始时间");
        }

        // 生成新文件 key
        String fileKey = exhibitionId + "/UploadRequirement/" + System.currentTimeMillis() + "_" + file.getOriginalFilename();
        System.out.println("生成的 fileKey (更新文件): " + fileKey);

        // 上传新文件到 COS
        String url = cosService.uploadFile(file, fileKey);
        System.out.println("COS 返回的 URL (更新文件): " + url);

        // 获取已有的蓝图要求记录
        BlueprintRequirement existing = blueprintRequirementMapper.selectByPrimaryKey(requirementId);
        if (existing == null) {
            throw new Exception("蓝图要求记录不存在");
        }

        // 保存原有的 cosKey
        String oldCosKey = existing.getCosKey();

        // 判断是否需要替换文件（即新生成的 key与原来的是否不同）
        if (oldCosKey != null && !oldCosKey.equals(fileKey)) {
            // 更新记录：将新文件 key 写入，同时更新其它字段
            existing.setCosKey(fileKey);
            existing.setBlueprintType(blueprintType);
            existing.setDescription(description);
            existing.setBoothType(boothType);
            existing.setStartTime(startTime);
            existing.setEndTime(endTime);

            int count = blueprintRequirementMapper.updateBlueprintRequirement(existing);
            if (count <= 0) {
                throw new Exception("更新蓝图要求失败");
            }
            // 删除旧文件
            cosService.deleteFile(oldCosKey);
        } else {
            // 如果 cosKey 未发生变化，则只更新其它字段
            existing.setBlueprintType(blueprintType);
            existing.setDescription(description);
            existing.setBoothType(boothType);
            existing.setStartTime(startTime);
            existing.setEndTime(endTime);
            int count = blueprintRequirementMapper.updateBlueprintRequirement(existing);
            if (count <= 0) {
                throw new Exception("更新蓝图要求失败");
            }
        }

        BlueprintRequirement updated = blueprintRequirementMapper.selectByPrimaryKey(requirementId);
        return convertToDTO(updated);
    }


    @Override
    public BlueprintRequirementDTO addRequirement(int exhibitionId,
                                                  String blueprintType,
                                                  String description,
                                                  String boothType,
                                                  Date startTime,
                                                  Date endTime,
                                                  MultipartFile file) throws Exception {
        System.out.println("开始添加蓝图要求记录，展会ID: " + exhibitionId);

        // 校验结束时间不能早于开始时间
        if (endTime.before(startTime)) {
            throw new Exception("结束时间不能早于开始时间");
        }

        // 生成文件 key
        String fileKey = exhibitionId + "/UploadRequirement/" + System.currentTimeMillis() + "_" + file.getOriginalFilename();
        System.out.println("生成的 fileKey: " + fileKey);

        // 上传文件到 COS
        String url = cosService.uploadFile(file, fileKey);
        System.out.println("COS 返回的 URL: " + url);

        // 构造蓝图要求记录
        BlueprintRequirement req = new BlueprintRequirement();
        req.setExhibitionId(exhibitionId);
        req.setBlueprintType(blueprintType);
        req.setDescription(description);
        req.setCosKey(fileKey);
        req.setStartTime(startTime);
        req.setEndTime(endTime);
        if (boothType == null || boothType.trim().isEmpty()) {
            req.setBoothType("both");
        } else {
            req.setBoothType(boothType);
        }

        int count = blueprintRequirementMapper.insertBlueprintRequirement(req);
        if (count <= 0) {
            System.out.println("添加蓝图要求失败");
            throw new Exception("添加蓝图要求失败");
        }
        System.out.println("添加成功，生成的 requirementId: " + req.getRequirementId());
        BlueprintRequirement inserted = blueprintRequirementMapper.selectByPrimaryKey(req.getRequirementId());
        return convertToDTO(inserted);
    }






}
