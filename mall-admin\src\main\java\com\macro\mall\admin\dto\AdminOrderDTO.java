package com.macro.mall.admin.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class AdminOrderDTO {
    private Long orderId;
    private Integer exhibitionId;
    private BigDecimal exhibitorDeposit;
    private BigDecimal siteManagementFee;
    private String status;
    private String auditStatus;
    private String depositStatus;
    private String invoiceStatus;
    private Date createTime;
    private Date updateTime;
    private String remark;
    // 订单项集合
    private List<AdminOrderItemDTO> orderItems;

    // getter、setter 方法
    public Long getOrderId() {
        return orderId;
    }
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
    public Integer getExhibitionId() {
        return exhibitionId;
    }
    public void setExhibitionId(Integer exhibitionId) {
        this.exhibitionId = exhibitionId;
    }

    public BigDecimal getExhibitorDeposit() {
        return exhibitorDeposit;
    }
    public void setExhibitorDeposit(BigDecimal exhibitorDeposit) {
        this.exhibitorDeposit = exhibitorDeposit;
    }
    public BigDecimal getSiteManagementFee() {
        return siteManagementFee;
    }
    public void setSiteManagementFee(BigDecimal siteManagementFee) {
        this.siteManagementFee = siteManagementFee;
    }

    public String getAuditStatus() {
        return auditStatus;
    }
    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }
    public String getDepositStatus() {
        return depositStatus;
    }
    public void setDepositStatus(String depositStatus) {
        this.depositStatus = depositStatus;
    }
    public String getInvoiceStatus() {
        return invoiceStatus;
    }
    public void setInvoiceStatus(String invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }
    public Date getCreateTime() {
        return createTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    public String getRemark() {
        return remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public List<AdminOrderItemDTO> getOrderItems() {
        return orderItems;
    }
    public void setOrderItems(List<AdminOrderItemDTO> orderItems) {
        this.orderItems = orderItems;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }




    /** 新增：展商信息 DTO */
    private ExhibitorDTO exhibitor;

    // getter/setter 略...
    public ExhibitorDTO getExhibitor() {
        return exhibitor;
    }
    public void setExhibitor(ExhibitorDTO exhibitor) {
        this.exhibitor = exhibitor;
    }





    /** 搭建商信息 */
    private BuilderDTO builder;


    public BuilderDTO getBuilder() {
        return builder;
    }

    public void setBuilder(BuilderDTO builder) {
        this.builder = builder;
    }


}
