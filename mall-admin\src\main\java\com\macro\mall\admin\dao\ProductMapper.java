package com.macro.mall.admin.dao;

import com.macro.mall.common.model.Product;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface ProductMapper {

    /**
     * 根据展会ID查询对应商城下的商品列表
     * @param exhibitionId 展会ID
     * @param offset 分页偏移量
     * @param pageSize 每页显示的商品数量
     * @return 商品列表
     */
    List<Product> selectProductsByExhibitionId(
            @Param("exhibitionId") Integer exhibitionId,
            @Param("offset") int offset,
            @Param("pageSize") int pageSize
    );

    /**
     * 根据产品ID查询产品信息
     * @param productId 产品ID
     * @return 产品对象
     */
    Product selectProductById(@Param("productId") int productId);

    /**
     * 更新商品信息
     * @param productId 产品ID
     * @param product 商品对象
     * @return 更新的行数
     */
    int updateProduct(@Param("productId") Integer productId, @Param("product") Product product);

    /**
     * 删除单个商品
     * @param productId 产品ID
     * @param exhibitionId 展会ID
     * @return 删除的行数
     */
    int deleteProduct(
            @Param("productId") Integer productId,
            @Param("exhibitionId") Integer exhibitionId
    );

    /**
     * 批量删除商品
     * @param productIds 商品ID列表
     * @param exhibitionId 展会ID
     * @return 删除的行数
     */
    int deleteProducts(
            @Param("productIds") List<Integer> productIds,
            @Param("exhibitionId") Integer exhibitionId
    );

    /**
     * 添加商品
     * @param product 商品对象
     * @return 插入成功的行数
     */
    int insertProduct(Product product);

    /**
     * 查询指定展会的商品总数
     * @param exhibitionId 展会ID
     * @return 商品总数
     */
    int selectTotalProductCount(int exhibitionId);




    /** 批量按 ID 查询商品（用于一键汇率换算） */
    List<Product> selectProductsByIds(@Param("ids") List<Integer> ids);

    /**
     * 只更新 price_usd 与 deposit_usd 字段
     */
    void updateProductAmounts(@Param("productId") Integer productId,
                              @Param("priceUsd")   BigDecimal priceUsd,
                              @Param("depositUsd") BigDecimal depositUsd);


}
