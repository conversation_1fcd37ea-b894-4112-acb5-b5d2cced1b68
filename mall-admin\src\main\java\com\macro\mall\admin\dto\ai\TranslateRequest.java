package com.macro.mall.admin.dto.ai;

import javax.validation.constraints.NotEmpty;

/**
 * 前端发起翻译请求时携带的数据
 */
public class TranslateRequest {


    /** 包含强制指令和中文内容 HTML 的完整 Prompt */
    @NotEmpty(message = "内容的 Prompt 不能为空")
    private String contentPrompt;

    /* ---------- 构造方法 ---------- */

    public TranslateRequest() {
    }

    public TranslateRequest(String contentPrompt) {
        this.contentPrompt = contentPrompt;
    }



    public String getContentPrompt() {
        return contentPrompt;
    }

    public void setContentPrompt(String contentPrompt) {
        this.contentPrompt = contentPrompt;
    }
}
