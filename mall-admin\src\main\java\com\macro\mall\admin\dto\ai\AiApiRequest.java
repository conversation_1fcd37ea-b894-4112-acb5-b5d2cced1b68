package com.macro.mall.admin.dto.ai;

import java.util.List;

/**
 * 发送给 DeepSeek API 的请求体结构
 */
public class AiApiRequest {

    /** 使用的模型 */
    private String model;

    /** 消息列表（包含系统指令和用户输入） */
    private List<Message> messages;

    /** 是否流式传输；后端固定为 false 即可 */
    private boolean stream = false;

    /* ----------  Getter / Setter ---------- */

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public List<Message> getMessages() {
        return messages;
    }

    public void setMessages(List<Message> messages) {
        this.messages = messages;
    }

    public boolean isStream() {
        return stream;
    }

    public void setStream(boolean stream) {
        this.stream = stream;
    }

    /* ----------  嵌套消息类 ---------- */
    public static class Message {

        /** 角色：system / user / assistant */
        private String role;

        /** 消息内容 */
        private String content;

        /* -------- 构造方法 -------- */

        public Message() {
        }

        public Message(String role, String content) {
            this.role = role;
            this.content = content;
        }

        /* -------- Getter / Setter -------- */

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }
}
