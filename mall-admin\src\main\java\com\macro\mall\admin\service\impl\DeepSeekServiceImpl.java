package com.macro.mall.admin.service.impl;

import com.macro.mall.admin.dto.ai.AiApiRequest;
import com.macro.mall.admin.dto.ai.AiApiResponse;
import com.macro.mall.admin.service.DeepSeekService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Service
public class DeepSeekServiceImpl implements DeepSeekService {

    private static final Logger log = LoggerFactory.getLogger(DeepSeekServiceImpl.class);

    private final RestTemplate restTemplate;

    @Value("${deepseek.api.key}")
    private String apiKey;

    @Value("${deepseek.api.url}")
    private String apiUrl;

    @Value("${deepseek.api.model}")
    private String defaultModel;

    public DeepSeekServiceImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public String callDeepSeekApi(String systemPrompt, String userPrompt, String model) {
        log.info("准备调用 DeepSeek API. 模型: {}", model);

        // 1️⃣  请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(apiKey);

        // 2️⃣  请求体
        AiApiRequest requestBody = new AiApiRequest();
        requestBody.setModel(model);

        List<AiApiRequest.Message> messages;
        if (systemPrompt != null && !systemPrompt.isEmpty()) {
            messages = Arrays.asList(
                    new AiApiRequest.Message("system", systemPrompt),
                    new AiApiRequest.Message("user", userPrompt)
            );
        } else {
            messages = Collections.singletonList(
                    new AiApiRequest.Message("user", userPrompt)
            );
        }

        requestBody.setMessages(messages);
        requestBody.setStream(false);

        HttpEntity<AiApiRequest> entity = new HttpEntity<>(requestBody, headers);

        // 3️⃣  发送 POST
        try {
            ResponseEntity<AiApiResponse> response =
                    restTemplate.postForEntity(apiUrl, entity, AiApiResponse.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                String content = response.getBody().getFirstChoiceContent();
                log.info("DeepSeek API 调用成功.");
                if (content != null) {
                    return content.trim();
                }
                log.warn("从 DeepSeek API 响应中获取到的内容为 null。");
                throw new RuntimeException("AI 服务返回了空内容");
            }

            log.error("DeepSeek API 调用失败，状态码: {}, 响应体: {}",
                    response.getStatusCode(), response.getBody());
            throw new RuntimeException("调用 AI 服务失败，状态码: " + response.getStatusCode());

        } catch (HttpClientErrorException e) {
            log.error("调用 DeepSeek API 时发生 HTTP 错误: {} - {}",
                    e.getStatusCode(), e.getResponseBodyAsString(), e);
            throw new RuntimeException(
                    "调用 AI 服务时发生 HTTP 错误: " +
                            e.getStatusCode() + " - " + e.getResponseBodyAsString(), e);

        } catch (RestClientException e) {
            log.error("调用 DeepSeek API 时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("调用 AI 服务时发生连接或处理错误: " + e.getMessage(), e);
        }
    }

    @Override
    public String callDeepSeekApi(String systemPrompt, String userPrompt) {
        return callDeepSeekApi(systemPrompt, userPrompt, this.defaultModel);
    }
}
