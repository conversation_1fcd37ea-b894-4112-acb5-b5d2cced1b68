<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.admin.dao.BlueprintRequirementMapper">
    <select id="findByExhibitionId" parameterType="int" resultType="com.macro.mall.common.model.BlueprintRequirement">
        SELECT * FROM blueprintrequirements
        WHERE exhibition_id = #{exhibitionId}
    </select>

    <select id="selectByPrimaryKey" parameterType="int" resultType="com.macro.mall.common.model.BlueprintRequirement">
        SELECT * FROM blueprintrequirements
        WHERE requirement_id = #{requirementId}
    </select>

    <update id="updateBlueprintRequirement" parameterType="com.macro.mall.common.model.BlueprintRequirement">
        UPDATE blueprintrequirements
        <set>
            blueprint_type = #{blueprintType},
            description = #{description},
            <if test="cosKey != null">
                cos_key = #{cosKey},
            </if>
            start_time = #{startTime},
            end_time = #{endTime},
            booth_type = #{boothType}
        </set>
        WHERE requirement_id = #{requirementId}
    </update>


    <insert id="insertBlueprintRequirement" parameterType="com.macro.mall.common.model.BlueprintRequirement" useGeneratedKeys="true" keyProperty="requirementId">
        INSERT INTO blueprintrequirements (exhibition_id, blueprint_type, description, cos_key, start_time, end_time, booth_type)
        VALUES (#{exhibitionId}, #{blueprintType}, #{description}, #{cosKey}, #{startTime}, #{endTime}, #{boothType})
    </insert>

    <delete id="deleteBlueprintRequirement" parameterType="int">
        DELETE FROM blueprintrequirements
        WHERE requirement_id = #{requirementId}
    </delete>
</mapper>
