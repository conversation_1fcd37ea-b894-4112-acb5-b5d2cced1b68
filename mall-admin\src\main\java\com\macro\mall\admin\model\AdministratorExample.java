package com.macro.mall.admin.model;

import java.util.ArrayList;
import java.util.List;

public class AdministratorExample {
    protected List<Criteria> oredCriteria = new ArrayList<>();

    public AdministratorExample() {
        oredCriteria = new ArrayList<>();
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = new Criteria();
        oredCriteria.add(criteria);
        return criteria;
    }

    public static class Criteria {
        protected List<String> conditions = new ArrayList<>();

        public Criteria andUsernameEqualTo(String username) {
            conditions.add("username = '" + username + "'");
            return this;
        }

        public Criteria andPasswordEqualTo(String password) {
            conditions.add("password = '" + password + "'");
            return this;
        }

        public List<String> getConditions() {
            return conditions;
        }
    }
}
