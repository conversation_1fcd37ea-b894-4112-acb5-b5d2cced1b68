package com.macro.mall.admin.service.impl;

import com.macro.mall.admin.dao.AdminMapper;
import com.macro.mall.admin.dto.AdminLoginResponseDTO;
import com.macro.mall.admin.service.AdminService;
import com.macro.mall.admin.model.Administrator;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.macro.mall.common.service.RedisService;

import java.util.Date;
import java.util.List;

@Service
public class AdminServiceImpl implements AdminService {

    @Autowired
    private AdminMapper adminMapper;

    @Autowired
    private RedisService redisService;

    // 从配置文件中注入 JWT 密钥
    @Value("${jwt.secret}")
    private String jwtSecret;

    @Override
    public AdminLoginResponseDTO login(String username, String password) {
        System.out.println("LOGIN was pressed");
        // 1. 根据用户名查询管理员
        Administrator admin = adminMapper.selectAdministratorByUsername(username);
        if (admin == null) {
            throw new RuntimeException("用户名不存在");
        }

        // 2. 校验密码（这里只是简单的明文密码校验，生产环境要加密处理）
        if (!password.equals(admin.getPassword())) {
            throw new RuntimeException("密码不正确");
        }

        // 3. 查询该管理员有权限的展览 ID 列表
        List<Integer> exhibitionIds = adminMapper.selectExhibitionIdsByAdminId(admin.getAdminId());
        System.out.println(exhibitionIds);
        // 4. 生成 JWT Token（不暴露 adminId 和 companyId）
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + 7200 * 1000L);  // 2小时有效期
        String jwtToken = Jwts.builder()
                .setSubject(username)
                .claim("adminId", admin.getAdminId())
                .claim("companyId", admin.getCompanyId())
                .claim("exhibitionIds", exhibitionIds) // 返回给前端可以管理的展览 ID
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(SignatureAlgorithm.HS256, jwtSecret.getBytes())
                .compact();

        // 5. 保存 token 与管理员相关的信息到 Redis
        redisService.set("ADMIN_TOKEN_" + jwtToken, admin.getAdminId(), 7200);  // token 与 adminId 绑定
        redisService.set("ADMIN_USER_TOKEN_" + username, jwtToken, 7200);  // token 与 username 绑定
        redisService.set("TOKEN_ADMIN_" + jwtToken, username, 7200);  // token 与 username 绑定

        // 6. 返回 token 和展览 ID 列表
        AdminLoginResponseDTO response = new AdminLoginResponseDTO();
        response.setToken(jwtToken);
        response.setExhibitionIds(exhibitionIds);

        return response;
    }

    @Override
    public void logout(String token) {
        // 通过 token 获取对应的管理员用户名
        Object usernameObj = redisService.get("TOKEN_ADMIN_" + token);
        if (usernameObj != null) {
            String username = usernameObj.toString();
            // 删除 token -> 管理员ID 的映射
            redisService.del("ADMIN_TOKEN_" + token);
            // 删除 管理员用户名 -> token 的映射
            redisService.del("ADMIN_USER_TOKEN_" + username);
            // 删除 token -> 管理员用户名 的映射
            redisService.del("TOKEN_ADMIN_" + token);
        } else {
            // 如果未查到对应的用户名，仍尝试删除 token 相关映射
            redisService.del("ADMIN_TOKEN_" + token);
            redisService.del("TOKEN_ADMIN_" + token);
        }
    }
}
