package com.macro.mall.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("更新展会信息(联系人)参数")
public class UpdateExhibitorContactParam {
    @ApiModelProperty("展商ID")
    private int exhibitorId;
    @ApiModelProperty("联系人")
    private String exhibitorName;
    @ApiModelProperty("手机号")
    private String phone;
    @ApiModelProperty("邮箱")
    private String email;

    // Getters & Setters
    public int getExhibitorId() {
        return exhibitorId;
    }
    public void setExhibitorId(int exhibitorId) {
        this.exhibitorId = exhibitorId;
    }

    public String getPhone() {
        return phone;
    }
    public void setPhone(String phone) {
        this.phone = phone;
    }
    public String getEmail() {
        return email;
    }
    public void setEmail(String email) {
        this.email = email;
    }

    public String getExhibitorName() {
        return exhibitorName;
    }

    public void setExhibitorName(String exhibitorName) {
        this.exhibitorName = exhibitorName;
    }
}
