package com.macro.mall.admin.service.impl;

import com.macro.mall.admin.dto.ExhibitionAnnouncementDTO;
import com.macro.mall.admin.dao.ExhibitionMapper;
import com.macro.mall.admin.dto.ExhibitionDTO;
import com.macro.mall.admin.dto.ExhibitionDepositRuleDTO;
import com.macro.mall.admin.dto.ExhibitionRushRuleDTO;
import com.macro.mall.common.model.Exhibition;
import com.macro.mall.admin.service.ExhibitionService;
import com.macro.mall.common.model.ExhibitionDepositRule;
import com.macro.mall.common.service.CosService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ExhibitionServiceImpl implements ExhibitionService {

    @Autowired
    private ExhibitionMapper exhibitionMapper;


    @Autowired
    private CosService cosService;

    //    @Override
//    public ExhibitionAnnouncementDTO getExhibitionAnnouncement(Integer exhibitionId) {
//        Exhibition exhibition = exhibitionMapper.selectByExhibitionId(exhibitionId);
//        if (exhibition != null) {
//            ExhibitionAnnouncementDTO dto = new ExhibitionAnnouncementDTO();
//            dto.setExhibitionId(exhibition.getExhibitionId());
//            dto.setAnnouncement(exhibition.getAnnouncement());
//            return dto;
//        }
//        return null;
//    }
@Override
@Transactional
public int updateExhibition(ExhibitionDTO dto) {

    /* ---------- 校验汇率 ---------- */
    if (dto.getUsdToRmbRate() != null) {
        BigDecimal rate = dto.getUsdToRmbRate();
        // 必须 >0 且不超 100（防止录错 725）
        if (rate.compareTo(BigDecimal.ZERO) <= 0
                || rate.compareTo(new BigDecimal("20")) > 0) {
            throw new IllegalArgumentException("非法汇率：" + rate);
        }
    }

    /* ---------- 更新展会基础表 ---------- */
    Exhibition expo = new Exhibition();
    BeanUtils.copyProperties(dto, expo);   // 会把 usdToRmbRate 复制进去
    int count = exhibitionMapper.updateExhibition(expo);

    /* ---------- 同步加急规则（原逻辑不变） ---------- */
    List<ExhibitionRushRuleDTO> dbRules =
            exhibitionMapper.selectRushRulesByExhibitionId(expo.getExhibitionId());
    List<ExhibitionRushRuleDTO> reqRules = dto.getRushPeriods();

    // 删除
    dbRules.stream()
            .filter(db -> reqRules.stream()
                    .noneMatch(r -> r.getRushRuleId() != null
                            && r.getRushRuleId().equals(db.getRushRuleId())))
            .forEach(db -> exhibitionMapper.deleteRushRuleById(db.getRushRuleId()));

    // 插入 / 更新
    for (ExhibitionRushRuleDTO r : reqRules) {
        if (r.getRushRuleId() == null) {
            exhibitionMapper.insertRushRule(expo.getExhibitionId(),
                    r.getRushStartTime(), r.getRushEndTime(),
                    r.getFeePercentageRmb(), r.getFeePercentageUsd());
        } else {
            ExhibitionRushRuleDTO origin = dbRules.stream()
                    .filter(x -> x.getRushRuleId().equals(r.getRushRuleId()))
                    .findFirst().orElse(null);
            if (origin != null &&
                    (!origin.getRushStartTime().equals(r.getRushStartTime())
                            || !origin.getRushEndTime().equals(r.getRushEndTime())
                            || !origin.getFeePercentageRmb().equals(r.getFeePercentageRmb())
                            || !origin.getFeePercentageUsd().equals(r.getFeePercentageUsd()))) {

                exhibitionMapper.updateRushRule(r.getRushRuleId(),
                        r.getRushStartTime(), r.getRushEndTime(),
                        r.getFeePercentageRmb(), r.getFeePercentageUsd());
            }
        }
    }

    return count;
}

    @Override
    public ExhibitionDTO getExhibitionDetailsById(Integer exhibitionId) {
        // ① 读出 DTO（含 cosKey）
        ExhibitionDTO dto = exhibitionMapper.selectExhibitionWithRushRules(exhibitionId);
        if (dto == null) {
            return null;
        }

        /* ② 把 cosKey ➟ URL */
        if (StringUtils.hasText(dto.getCoverImagePath())) {
            dto.setCoverImagePath(
                    cosService.getFullUrl(dto.getCoverImagePath())   // ★ 关键一行
            );
        }

        /* ③ 过滤 rushPeriods（原逻辑保持） */
        if (dto.getRushPeriods() != null) {
            dto.setRushPeriods(
                    dto.getRushPeriods().stream()
                            .filter(p -> p.getFeePercentageRmb() != null &&
                                    p.getFeePercentageUsd() != null)
                            .collect(Collectors.toList()));
        }
        return dto;
    }

// ================= 新增(押金规则) =================

    @Override
    public List<ExhibitionDepositRuleDTO> listDepositRulesByExhibition(Integer exhibitionId) {
        List<ExhibitionDepositRule> ruleList = exhibitionMapper.selectDepositRulesByExhibitionId(exhibitionId);

        // 转 DTO
        List<ExhibitionDepositRuleDTO> dtoList = ruleList.stream().map(rule -> {
            ExhibitionDepositRuleDTO dto = new ExhibitionDepositRuleDTO();
            BeanUtils.copyProperties(rule, dto);
            return dto;
        }).collect(Collectors.toList());

        return dtoList;
    }
    @Override
    @Transactional
    public int createOrUpdateDepositRule(ExhibitionDepositRuleDTO ruleDTO) {
        // DTO -> Model
        ExhibitionDepositRule depositRule = new ExhibitionDepositRule();
        BeanUtils.copyProperties(ruleDTO, depositRule);

        if (depositRule.getRuleId() == null) {
            // 新增
            return exhibitionMapper.insertDepositRule(depositRule);
        } else {
            // 更新
            return exhibitionMapper.updateDepositRule(depositRule);
        }
    }

    @Override
    @Transactional
    public int deleteDepositRule(Integer ruleId) {
        return exhibitionMapper.deleteDepositRuleById(ruleId);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> uploadCover(Integer exhibitionId,
                                           MultipartFile file) {

        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }

        /* ---------- 1. 生成 key ---------- */
        String ext = StringUtils.getFilenameExtension(file.getOriginalFilename());
        String fileKey = String.format("%d/exhibitionImage/%d.%s",
                exhibitionId,
                System.currentTimeMillis(),
                ext != null ? ext : "jpg");

        /* ---------- 2. 上传到 COS ---------- */
        String url = cosService.uploadFile(file, fileKey);

        /* ---------- 3. 写数据库 ---------- */
        int rows = exhibitionMapper.updateCoverKey(exhibitionId, fileKey);
        if (rows == 0) {
            // exhibitionId 不存在——抛异常触发事务回滚（如需自定义异常可替换）
            throw new RuntimeException("写数据库失败，exhibitionId=" + exhibitionId);
        }

        /* ---------- 4. 返回给 Controller ---------- */
        Map<String, String> map = new HashMap<>(2);
        map.put("cosKey", fileKey);
        map.put("url",    url);
        return map;
    }

}
