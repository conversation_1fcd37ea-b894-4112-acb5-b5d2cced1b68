package com.macro.mall.admin.dao;

import com.macro.mall.admin.dto.AdminOrderDTO;
import com.macro.mall.admin.dto.AdminOrderItemDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface AdminOrderMapper {

    /**
     * 根据展会ID分页查询订单列表（仅订单主表信息，不含订单项详情）
     */
    List<AdminOrderDTO> selectOrdersByExhibitionId(Map<String, Object> params);

    /**
     * 查询订单总数（根据展会ID）
     */
    int selectTotalOrderCount(@Param("exhibitionId") Integer exhibitionId);

    /**
     * 根据订单ID查询订单详情（包含订单项详情）
     */
    AdminOrderDTO selectOrderById(@Param("orderId") Long orderId);

    /**
     * 更新订单项（编辑商品/押金）
     * @param orderItemDTO 订单项 DTO
     * @return 更新影响的行数
     */
    int updateOrderItem(AdminOrderItemDTO orderItemDTO);

    /**
     * 新增订单项（新增商品，product_id 固定为 -1）
     * @param orderItemDTO 订单项 DTO
     * @return 插入影响的行数
     */
    int addOrderItem(AdminOrderItemDTO orderItemDTO);

    /**
     * 更新订单中的场地管理费
     * @param orderId 订单ID
     * @param siteManagementFee 新的场地管理费
     * @return 更新影响的行数
     */
    int updateSiteManagementFee(Long orderId, BigDecimal siteManagementFee);

    /**
     * 更新订单项中的商品押金
     * @param params 包含 orderItemId 和 deposit 等参数
     * @return 更新影响的行数
     */
    int updateProductDeposit(Map<String, Object> params);


    /**
     * 更新订单中的展览押金
     * @param params 包含 orderId 和 deposit 等参数
     * @return 更新影响的行数
     */
    int updateExhibitorDeposit(Map<String, Object> params);



    /**
     * 删除订单项
     * @param orderItemId 订单项ID
     * @return 删除影响的行数
     */
    int deleteOrderItem(Long orderItemId);


    /**
     * 更新订单的审核状态（approved/rejected）
     * @param params 包含 orderId 和 auditStatus 参数
     * @return 更新影响的行数
     */
    int updateAuditStatus(Map<String, Object> params);


    /**
     * 更新订单备注
     * @param params 包含 orderId 和 remark 参数
     * @return 更新影响的行数
     */
    int updateRemark(Map<String, Object> params);


}
