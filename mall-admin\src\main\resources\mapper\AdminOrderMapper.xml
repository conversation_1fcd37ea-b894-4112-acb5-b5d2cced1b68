<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.macro.mall.admin.dao.AdminOrderMapper">

    <!-- 分页查询订单列表 -->
    <select id="selectOrdersByExhibitionId"
            resultMap="AdminOrderWithExhibitorAndBuilderItemsMap"
            parameterType="map">
        SELECT
            o.order_id,
            o.exhibition_id,
            o.exhibitor_deposit,
            o.site_management_fee,
            o.status,
            o.audit_status,
            o.deposit_status,
            o.invoice_status,
            o.create_time,
            o.update_time,
            o.remark,

            oi.order_item_id,
            oi.product_id,
            oi.product_name,
            oi.product_name_en,
            oi.quantity,
            oi.unit_price,
            oi.subtotal,
            oi.deposit,
            oi.create_time AS item_create_time,
            oi.update_time AS item_update_time,

            -- 展商字段
            e.company_name      AS exhibitor_company_name,
            e.phone             AS exhibitor_phone,
            e.email             AS exhibitor_email,
            e.booth_type        AS exhibitor_booth_type,
            e.booth_number      AS exhibitor_booth_number,
            e.exhibition_hall   AS exhibitor_exhibition_hall,
            e.booth_area        AS exhibitor_booth_area,
            e.exhibitor_name    AS exhibitor_name,

            -- 搭建商字段
            b.builder_company           AS builder_company,
            b.builder_contact_name      AS builder_contact_name,
            b.builder_contact_phone     AS builder_contact_phone,
            b.builder_contact_email     AS builder_contact_email
        FROM `order` o
                 LEFT JOIN order_item oi ON o.order_id = oi.order_id
                 LEFT JOIN exhibitor e    ON o.exhibitor_id = e.exhibitor_id
                 LEFT JOIN builder b      ON e.exhibitor_id = b.exhibitor_id
        WHERE o.exhibition_id = #{exhibitionId}
        ORDER BY o.create_time DESC
            LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 查询订单总数 -->
    <select id="selectTotalOrderCount" resultType="int" parameterType="java.lang.Integer">
        SELECT COUNT(1)
        FROM `order`
        WHERE exhibition_id = #{exhibitionId}
    </select>

    <!-- 查询订单详情（含订单项、展商、搭建商） -->
    <select id="selectOrderById"
            parameterType="long"
            resultMap="AdminOrderWithExhibitorAndBuilderItemsMap">
        SELECT
            o.order_id,
            o.exhibition_id,
            o.exhibitor_deposit,
            o.site_management_fee,
            o.status,
            o.audit_status,
            o.deposit_status,
            o.invoice_status,
            o.create_time,
            o.update_time,
            o.remark,

            oi.order_item_id,
            oi.product_id,
            oi.product_name,
            oi.product_name_en,
            oi.quantity,
            oi.unit_price,
            oi.subtotal,
            oi.deposit,
            oi.create_time      AS item_create_time,
            oi.update_time      AS item_update_time,

            -- 展商字段
            e.company_name      AS exhibitor_company_name,
            e.phone             AS exhibitor_phone,
            e.email             AS exhibitor_email,
            e.booth_type        AS exhibitor_booth_type,
            e.booth_number      AS exhibitor_booth_number,
            e.exhibition_hall   AS exhibitor_exhibition_hall,
            e.booth_area        AS exhibitor_booth_area,
            e.exhibitor_name    AS exhibitor_name,

            -- 搭建商字段
            b.builder_company           AS builder_company,
            b.builder_contact_name      AS builder_contact_name,
            b.builder_contact_phone     AS builder_contact_phone,
            b.builder_contact_email     AS builder_contact_email

        FROM `order` o
                 LEFT JOIN order_item oi ON o.order_id = oi.order_id
                 LEFT JOIN exhibitor e    ON o.exhibitor_id = e.exhibitor_id
                 LEFT JOIN builder b      ON e.exhibitor_id = b.exhibitor_id
        WHERE o.order_id = #{orderId}
    </select>


    <!-- 更新订单项（编辑商品/押金） -->
    <update id="updateOrderItem" parameterType="com.macro.mall.admin.dto.AdminOrderItemDTO">
        UPDATE order_item
        SET product_name = #{productName},
            unit_price = #{unitPrice},
            quantity = #{quantity},
            subtotal = #{subtotal},
            update_time = NOW()
        WHERE order_item_id = #{orderItemId}
    </update>

    <!-- 新增订单项（新增商品，product_id=-1） -->
    <insert id="addOrderItem" parameterType="com.macro.mall.admin.dto.AdminOrderItemDTO">
        INSERT INTO order_item (order_id, product_id, product_name, unit_price, quantity, subtotal, create_time, update_time)
        VALUES (#{orderId}, -1, #{productName}, #{unitPrice}, #{quantity}, #{subtotal}, NOW(), NOW())
    </insert>

    <!-- 更新订单的场地管理费 -->
    <update id="updateSiteManagementFee" parameterType="map">
        UPDATE `order`
        SET site_management_fee = #{siteManagementFee},
            update_time = NOW()
        WHERE order_id = #{orderId}
    </update>

    <!-- 更新订单项中商品押金 -->
    <update id="updateProductDeposit" parameterType="map">
        UPDATE order_item
        SET deposit = #{deposit},
            update_time = NOW()
        WHERE order_item_id = #{orderItemId}
    </update>


    <!-- 更新订单中的 exhibitor_deposit（展览押金） -->
    <update id="updateExhibitorDeposit" parameterType="map">
        UPDATE `order`
        SET exhibitor_deposit = #{deposit},
            update_time = NOW()
        WHERE order_id = #{orderId}
    </update>



    <!-- 删除订单项 -->
    <delete id="deleteOrderItem" parameterType="long">
        DELETE FROM order_item WHERE order_item_id = #{orderItemId}
    </delete>

    <update id="updateAuditStatus" parameterType="map">
        UPDATE `order`
        SET audit_status = #{auditStatus},
            update_time = NOW()
        WHERE order_id = #{orderId}
    </update>


    <update id="updateRemark" parameterType="map">
        UPDATE `order`
        SET remark = #{remark},
            update_time = NOW()
        WHERE order_id = #{orderId}
    </update>

    <!-- 结果映射：订单 + 展商 + 搭建商（不含订单项） -->
    <resultMap id="AdminOrderWithExhibitorAndBuilderMap" type="com.macro.mall.admin.dto.AdminOrderDTO">
        <id property="orderId"           column="order_id"/>
        <result property="exhibitionId"      column="exhibition_id"/>
        <result property="exhibitorDeposit"  column="exhibitor_deposit"/>
        <result property="siteManagementFee" column="site_management_fee"/>
        <result property="status"            column="status"/>
        <result property="auditStatus"       column="audit_status"/>
        <result property="depositStatus"     column="deposit_status"/>
        <result property="invoiceStatus"     column="invoice_status"/>
        <result property="createTime"        column="create_time"/>
        <result property="updateTime"        column="update_time"/>
        <result property="remark"            column="remark"/>

        <!-- 展商信息 -->
        <association property="exhibitor" javaType="com.macro.mall.admin.dto.ExhibitorDTO">
            <result property="companyName"     column="exhibitor_company_name"/>
            <result property="phone"           column="exhibitor_phone"/>
            <result property="email"           column="exhibitor_email"/>
            <result property="boothType"       column="exhibitor_booth_type"/>
            <result property="boothNumber"     column="exhibitor_booth_number"/>
            <result property="exhibitionHall"  column="exhibitor_exhibition_hall"/>
            <result property="boothArea"       column="exhibitor_booth_area"/>
            <result property="exhibitorName"   column="exhibitor_name"/>
        </association>

        <!-- 搭建商信息 -->
        <association property="builder" javaType="com.macro.mall.admin.dto.BuilderDTO">
            <result property="builderCompany"      column="builder_company"/>
            <result property="builderContactName"  column="builder_contact_name"/>
            <result property="builderContactPhone" column="builder_contact_phone"/>
            <result property="builderContactEmail" column="builder_contact_email"/>
        </association>
    </resultMap>

    <!-- 结果映射：订单 + 订单项 + 展商 + 搭建商 -->
    <resultMap id="AdminOrderWithExhibitorAndBuilderItemsMap" type="com.macro.mall.admin.dto.AdminOrderDTO">
        <id property="orderId"           column="order_id"/>
        <result property="exhibitionId"      column="exhibition_id"/>
        <result property="exhibitorDeposit"  column="exhibitor_deposit"/>
        <result property="siteManagementFee" column="site_management_fee"/>
        <result property="status"            column="status"/>
        <result property="auditStatus"       column="audit_status"/>
        <result property="depositStatus"     column="deposit_status"/>
        <result property="invoiceStatus"     column="invoice_status"/>
        <result property="createTime"        column="create_time"/>
        <result property="updateTime"        column="update_time"/>
        <result property="remark"            column="remark"/>

        <!-- 展商信息 -->
        <association property="exhibitor" javaType="com.macro.mall.admin.dto.ExhibitorDTO">
            <result property="companyName"     column="exhibitor_company_name"/>
            <result property="phone"           column="exhibitor_phone"/>
            <result property="email"           column="exhibitor_email"/>
            <result property="boothType"       column="exhibitor_booth_type"/>
            <result property="boothNumber"     column="exhibitor_booth_number"/>
            <result property="exhibitionHall"  column="exhibitor_exhibition_hall"/>
            <result property="boothArea"       column="exhibitor_booth_area"/>
            <result property="exhibitorName"   column="exhibitor_name"/>
        </association>

        <!-- 搭建商信息 -->
        <association property="builder" javaType="com.macro.mall.admin.dto.BuilderDTO">
            <result property="builderCompany"      column="builder_company"/>
            <result property="builderContactName"  column="builder_contact_name"/>
            <result property="builderContactPhone" column="builder_contact_phone"/>
            <result property="builderContactEmail" column="builder_contact_email"/>
        </association>

        <!-- 订单项集合 -->
        <collection property="orderItems" ofType="com.macro.mall.admin.dto.AdminOrderItemDTO">
            <id property="orderItemId" column="order_item_id"/>
            <result property="productId"   column="product_id"/>
            <result property="productName"   column="product_name"/>
            <result property="productNameEn"   column="product_name_en"/>
            <result property="quantity"    column="quantity"/>
            <result property="unitPrice"   column="unit_price"/>
            <result property="subtotal"    column="subtotal"/>
            <result property="deposit"     column="deposit"/>
            <result property="createTime"  column="item_create_time"/>
            <result property="updateTime"  column="item_update_time"/>
        </collection>
    </resultMap>



</mapper>
