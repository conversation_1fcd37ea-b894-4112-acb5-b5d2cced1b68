package com.macro.mall.admin.dao;

import com.macro.mall.common.model.Fascia;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface FasciaMapper {

    /**
     * 根据 exhibitorId 查询 fascia 记录
     * @param exhibitorId 展商ID
     * @return Fascia 记录（如果有则返回，否则返回 null）
     */
    Fascia selectByExhibitorId(@Param("exhibitorId") int exhibitorId);

    /**
     * 插入 fascia 记录
     * @param record fasia 记录
     * @return 影响的记录数
     */
    int insertSelective(Fascia record);

    /**
     * 选择性更新 fascia 记录
     * @param record fasia 记录
     * @return 影响的记录数
     */
    int updateByPrimaryKeySelective(Fascia record);
}
