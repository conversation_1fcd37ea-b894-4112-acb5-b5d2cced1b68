package com.macro.mall.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("蓝图数据传输对象")
public class BlueprintDTO {
    @ApiModelProperty("蓝图ID")
    private int blueprintId;

    @ApiModelProperty("需求ID")
    private int requirementId;

    @ApiModelProperty("文件路径") // 前端显示完整文件路径
    private String filePath;

    @ApiModelProperty("图纸类型")
    private String blueprintType;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("展商公司名称")
    private String exhibitorCompany;

    @ApiModelProperty("展商联系电话")
    private String exhibitorPhone;

    @ApiModelProperty("展商联系人")
    private String exhibitorName;

    @ApiModelProperty("搭建商公司名称")
    private String builderCompany;

    @ApiModelProperty("搭建商联系人")
    private String builderContactName;

    @ApiModelProperty("搭建商联系电话")
    private String builderContactPhone;

    // Getters and Setters
    public int getBlueprintId() {
        return blueprintId;
    }
    public void setBlueprintId(int blueprintId) {
        this.blueprintId = blueprintId;
    }
    public int getRequirementId() {
        return requirementId;
    }
    public void setRequirementId(int requirementId) {
        this.requirementId = requirementId;
    }
    public String getFilePath() {
        return filePath;
    }
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    public String getBlueprintType() {
        return blueprintType;
    }
    public void setBlueprintType(String blueprintType) {
        this.blueprintType = blueprintType;
    }
    public String getStatus() {
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }
    public String getExhibitorCompany() {
        return exhibitorCompany;
    }
    public void setExhibitorCompany(String exhibitorCompany) {
        this.exhibitorCompany = exhibitorCompany;
    }
    public String getExhibitorPhone() {
        return exhibitorPhone;
    }
    public void setExhibitorPhone(String exhibitorPhone) {
        this.exhibitorPhone = exhibitorPhone;
    }
    public String getExhibitorName() {
        return exhibitorName;
    }
    public void setExhibitorName(String exhibitorName) {
        this.exhibitorName = exhibitorName;
    }
    public String getBuilderCompany() {
        return builderCompany;
    }
    public void setBuilderCompany(String builderCompany) {
        this.builderCompany = builderCompany;
    }
    public String getBuilderContactName() {
        return builderContactName;
    }
    public void setBuilderContactName(String builderContactName) {
        this.builderContactName = builderContactName;
    }
    public String getBuilderContactPhone() {
        return builderContactPhone;
    }
    public void setBuilderContactPhone(String builderContactPhone) {
        this.builderContactPhone = builderContactPhone;
    }
}
